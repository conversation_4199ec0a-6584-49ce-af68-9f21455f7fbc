en:
  emails:
    to_researchers:
      notify_researchers_patient_action_required:
        body: "Subject %{patient_code} from site %{clinical_center_code} in %{clinical_protocol_code} clinical trial needs assistance. Please resolve the following reimbursement issues at your earliest convenience: <br><br>%{reason_for_action}<br>"
      send_session_history_report:
        subject: "PAYCLINICAL: Login sessions"
      account_locked:
        subject: "ALERT: Your account has been locked"
        unlock_account_link_html: Unlock my account
        body_html: "After three failed login attempts your account has been locked until %{locked_until_time}
         on %{locked_until_day}.<br><br>Please click the following link to unlock your account earlier:<br>%{unlock_account_link}"
      successful_login_from_different_ip:
        subject: "ALERT: You have signed in from a new network"
        using_different_ip: "from a new network (estimated location: %{city}/%{country})"
        using_new_ip_or_location: "from a new network (IP address: %{ip})"
        using_new_ip_or_location_no_details: "from a new network"
        change_password_link_html: Change my password immediately
        block_account_link_html: Lock my account for 24 hours
        disable_notifications_link_html: Turn off this notification
        body_html: "This is to notify you about a successful login to your PayClinical account %{location} on %{day} at %{hour}.<br>
        <br>SECURITY ALERT: If you haven't signed in to your PayClinical account please click the following link to change your password and to inform us about an unauthorized access:<br>
         %{change_password_link_html}<br><br>Please click the following link to lock my account
          for next 24 hours:<br>%{block_account_link_html}<br><br>Please click the link below
           to disable notifications on login attempts from new networks:<br>%{disable_notifications_link_html}"
      account_locked_change_password:
        subject: "ALERT: Your account has been locked for security reasons"
        body: "Your account has been temporarily locked for security reasons.<br><br>Please click the following link
         to unlock your account and change your password:<br>%{change_pass_link}"
        change_pass_link: Unlock my account
      failed_login_attempt:
        subject: "ALERT: Failed login attempt"
        info: "This is to notify you about %{attempt_number} failed login attempt to your PayClinical account
         on %{date} at %{time}%{location_string}.<br>"
        lock_acc_link: Lock my account for 24 hours
        lock_acc_section: "<br>Please click the following link to lock your account for 24 hours
        :<br>%{lock_acc_link}<br>"
        disable_notifications_link: Turn off this notification
        disable_notifications_section: "<br>Please click the following link to disable the notifications:
         <br>%{disable_notifications_link}"
      account_self_locked:
        subject: "NOTIFICATION: Your account has been locked"
        body: "Your account has been locked until %{locked_until_time} on %{locked_until_day}.
        <br><br>Please click the following link to unlock your account earlier:<br>%{unlock_acc_link}"
        unlock_acc_link: "Unlock my account"
      password_changed:
        subject: "ALERT: Your password has been changed"
        lock_acc_link: Lock my account for 24 hours
        body: "Your password has been changed on %{password_changed_at} (%{location_text}).<br><br>
        SECURITY NOTE: If you haven't changed your password, click the following link to temporarily lock your account:<br>%{lock_acc_link}"
      send_pds_remider_to_cra:
        subject: "Przypomnienie o zestawieniu oczekującym na podpis (%{clinical_protocol_code}/%{clinical_center_code})"
        link_to_sign: "Złożenie podpisu cyfrowego"
        body: "Uprzejmie przypominamy, że poniższe zestawienie przelewów dla pacjentów stale oczekuje na Państwa
         podpis:</br></br>Tytuł badania: %{clinical_protocol_code}</br>Sponsor: %{project_name}</br></br>
         Ośrodek: %{site_name_city}</br>Okres: %{period}</br>Liczba przelewów: %{nr_of_transfers}<br/>
         Suma przelewów: %{transfers_total_amount} PLN<br/>Numer dokumentu: %{doc_nr}</br></br>
         Prosze kliknąć poniżej, aby podpisać zestawienie przelewów:</br>%{sign_link}"
      send_transfer_confirmation_to_transfer_ordering_researcher:
        subject: "SUBJECT REIMBURSEMENT: Site %{cc_code} in %{protocol_code} clinical trial"
        body: "Please find attached the confirmation of payments sent to subjects from site %{cc_code} in %{protocol_code} clinical trial."
        disable_info: "If you don’t want to receive payment notifications, please click the link below:"
        disable_link: "Turn off payment notifications"
      amount_treshold_notification:
        subject: 'ZwrotKosztów: Saldo projektu jest niskie'
        body: "Saldo Państwa projektu %{project_name} (%{clinical_protocol_code})
         wynosi: %{balance}. Zmalało poniżej ustalonego progu: %{amount_treshold_notfication}."
      send_visit_reminder:
        subject: "REMINDER: please reimburse %{patient_code}/%{clinical_protocol_code} for a visit %{visit_name}"
        body: "Please reimburse the subject %{patient_code} in %{clinical_protocol_code} clinical study for
         a visit %{visit_name} (visit date: %{visit_date}, due amount: %{amount}).
          This reminder has been sent by %{reminder_title} %{reminderer_last_name} (%{reminderer_role})."
      send_register_confirmation:
        subject: 'NOTIFICATION: Your PayClinical account has been activated'
        manuals_link: Read manuals
        body: "You currently have the following PayClinical roles:<br>%{researcher_roles_section}"
      send_reset_password_link_to_researcher:
        subject_one_non_demo_project: "ACTIVATION REQUEST: Subject reimbursement in %{protocol_code} clinical trial"
        subject_many_non_demo_projects: "ACTIVATION REQUEST: Subject reimbursement in %{protocol_code} and other clinical trials"
        subject_demo_only: "ACTIVATION REQUEST: Subject reimbursement in clinical trials"
        body_top_one_non_demo_project: "You have been invited to activate your PayClinical account to facilitate subject reimbursement in %{protocol_code} clinical trial."
        body_top_many_non_demo_projects: "You have been invited to activate your PayClinical account to facilitate subject reimbursement in %{protocol_code} and other clinical trials."
        body_top_demo_only: "You have been invited to activate your PayClinical account to facilitate subject reimbursement in clinical trials."
        body_bottom: "<br><br>Please click the following link to activate your %{role} account:"
        email_link: 'Account activation'
      project_waiting_transfers_notification:
        subject: 'APPROVAL REQUIRED: Subject reimbursement at site %{cc_code} in %{protocol_code} clinical trial'
        approve_link: "Review and approve"
        body: "There are  payments to subjects in  study awaiting your approval<br><br>Please click on a link below to approve the payments<br>%{approve_link}"
      waiting_transfer_notification:
        subject: 'APPROVAL REQUIRED: An urgent payment for patient %{patient_code} from site %{clinical_center_code} in %{clinical_protocol_code} clinical trial'

      note_corrections:
        send_to_cro:
          subject: "CREDIT NOTE: %{note_number} (%{protocol_code}/%{sponsor})"
      notify_managers_about_ready_project_debit_summary:
        subject: "DEBIT NOTE: %{note_number} (%{protocol_code}/%{sponsor})"
        body_short: "W załączeniu przesyłamy Państwu notę obciążeniową nr %{note_number} z tytułu zwrotu kosztów dla pacjentów wraz z listą przelewów w formacie Excel."

      waiting_transfers_list_in_clinical_center:
        subject: "Przelewy oczekujące na akceptację (%{clinical_protocol_code}/%{clinical_center_code})"
        patients_accounts_link: 'Konta rozliczeniowe pacjentów'
        body: "W załączeniu odnajdą Państwo zestawienie przelewów do pacjentów z ośrodka %{clinical_center_code}
         uczestniczących w badaniu klinicznym %{clinical_protocol_code}, które oczekują na Państwa akceptację.<br>
         <br>Liczba oczekujących przelewów: %{transfers_size}<br>Łączna kwota przelewów: %{transfers_sum} PLN<br>
         <br>Aby zweryfikować i zaakceptować oczekujące płatności, proszę kliknąć na link poniżej:<br>%{patients_accounts_link}"
      notify_managers_waiting_transfers:
        subject: "Przelew oczekujący na akceptację (%{clinical_protocol_code}, %{clinical_user_code} %{visit_name})"
        body: "Przelew do pacjenta %{patient_code} w kwocie %{transfer_amount} PLN z tytułu zwrotu kosztów za
               uczestnictwo w wizycie %{visit_name} w ramach badania klinicznego %{clinical_protocol_code}
               został zlecony przez: %{researcher_full_name} (%{researcher_role})
               i oczekuje na Państwa akceptację.</br></br>
               Proszę kliknąć na link poniżej, aby zaakceptować przelew:</br>"
        link: "Przelewy oczekujące na akceptację"
      send_authorization_list:
        subject: "NOTIFICATION: Your current PayClinical roles"
        body: "You currently have the following PayClinical roles: %{roles}"
        role_with_ccs: "%{project_role} in %{clinical_protocol_code} clinical trial for sites: %{clinical_centers}"
        role_with_cc: "%{project_role} in %{clinical_protocol_code} clinical trial for site %{clinical_centers}"
        role_without_cc: "%{project_role} in %{clinical_protocol_code} clinical trial"
      clincial_user_form_confirmed_researcher_notification:
        subject: "Konto pacjenta %{clinical_user_code} w badaniu %{clinical_protocol_code} zostało aktywowane"
        body: "Konto płatnicze pacjenta %{clinical_user_code} uczestniczącego w badaniu klinicznym %{clinical_protocol_code}
               w ośrodku %{clinical_center_code} zostało aktywowane zgodnie z otrzymanym formularzem danych osobowych.<br/>"
      invite_already_registered_researcher_to_project:
        subject: "INVITATION: Subject reimbursement in %{clinical_protocol_code} clinical study"
        body: "You have been invited to facilitate subject reimbursement in %{clinical_protocol_code} clinical trial.<br><br>Please click the following link to sign in:"
      notify_quintiles_cras:
        subject: "Zestawienie do podpisu (%{clinical_protocol_code}/%{clinical_center_code})"
        body: "Kod protokołu: %{clinical_protocol_code}
              <br>
              Tytuł badania: %{clinical_trial_title}
              <br>
              Sponsor: %{sponsor}
              <br>
              <br>
              Ośrodek: %{site_info}
              <br>
              Okres: %{period}
              Liczba przelewów: %{transfers_count}
              <br>
              Suma przelewów: %{transfers_amount} PLN
              <br>
              Numer dokumentu: %{full_note_number}
              <br>
              Prosze kliknąć poniżej, aby podpisać zestawienie przelewów:
              <br>"
        link: "Złożenie podpisu cyfrowego"

      notify_quintiles_cra_seperate:
        subject: "SIGNATURE REQUIRED: Payment batch# (%{note_number})"
        remind: "This is to remind you that the attached payment batch requires your approval."
        not_remind: "The attached payment batch requires your approval."
        body: "<br>
              <br>
              Study protocol: %{clinical_protocol_code}
              <br>
              Sponsor: %{sponsor}
              <br>
              Site: %{site_info}
              <br>
              Period: %{period}
              <br>
              Number of payments: %{transfers_count}
              <br>
              Total amount: %{transfers_amount} PLN
              <br>
              Payment batch: %{full_note_number}
              <br>
              <br>
              Please click the following link to sign this payment batch:
              <br>"
        link: "Sign digitally"

      clinical_centers_reports:
        subject: "Raporty z ośrodków w projekcie %{project_name}"
        body: "Kod protokołu: %{clinical_protocol_code}
              <br>
              Tytuł badania: %{clinical_trial_title}
              <br>
              Sponsor: %{sponsor}
              <br>
              <br>
              W załączniku znajdują się raporty z ośrodków (pdf).
              <br>"

      send_invoices:
        subject: "%{invoice_pluralized} za usługi płatnicze (%{date})"
        body: "W załączeniu przesyłamy Państwu faktury za usługi płatnicze świadczone od dnia %{date_from}
         do dnia %{date_to} z tytułu zwrotu kosztów pacjentom uczestniczącym w badaniach klinicznych %{protocol_codes}."

      pds_attachment_signed_notification:
        subject: "NOTIFICATION: Payment batch# %{note_number} has been signed "
        body: The attached payment batch# %{note_number} has been signed on %{signature_date}. Thank you.

      send_project_personnel_report:
        subject: "Raport ze zmian w ilości badaczy/pacjentów"
        body: "Pozdrawiam"

      new_patients_in_clinical_center:
        subject_single: "Nowe konto pacjenta %{patient_code} w ośrodku %{clinical_center_code} zostało aktywowane (badanie %{project_protocol_code})"
        subject_plurar: "Nowe konta pacjentów w ośrodku %{clinical_center_code} zostały aktywowane (badanie %{project_protocol_code})"
        body:
            1patient: "Konto płatnicze pacjenta %{patient_code} uczestniczącego
                        w badaniu klinicznym %{project_protocol_code}
                        w ośrodku %{clinical_center_code} zostało aktywowane zgodnie z otrzymanym
                        formularzem danych osobowych."

            patients: "Konta płatnicze pacjentów %{patients_comma_seperated} uczestniczących
                       w badaniu klinicznym %{project_protocol_code}
                       w ośrodku %{clinical_center_code} zostały aktywowane zgodnie z otrzymanymi
                       formularzami danych osobowych"
        footer_if_manager:
          body: "Jeżeli mają Państwo uprawnienia Managera i chcą Państwo wyłączyć powiadomienia o nowych pacjentach, proszę kliknąć na link poniżej:"
          link: "Zmień powiadomienia"

      send_pd_and_invoices:
        subject: "%{clinical_protocol_code} - faktura za okres %{start_date} - %{stop_date}"
        body:
