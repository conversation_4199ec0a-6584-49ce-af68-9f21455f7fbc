class DomainConstraint
  def initialize(domain)
    @domains = [domain].flatten
  end

  def matches?(request)
    @domains.include? request.domain
  end
end

Rails.application.routes.draw do
  if Rails.env.production?
    mount RailsPerformance::Engine, at: 'admin/performance'
  end

  constraints DomainConstraint.new(Rails.env.development? ? 'badaniekliniczne.test' : 'badaniekliniczne.pl') do
    match '/' => 'badanie_kliniczne/pages#show', via: :get, constraints: { subdomain: /.+/ }
    resources :form_fillings, only: :create, module: 'badanie_kliniczne'

    resource :clinical_user_home, only: [:index], controller: 'clinical_user_home'
    root to: 'clinical_user_home#index'
    get 'opinia', to: 'subject_surveys/survey_responses#new'
  end

  namespace :subject_surveys do
    resource :session, only: [:new, :create]
    resources :survey_responses, only: [:new, :create]
    resource :thank_you, only: [:show]
  end

  namespace :site_payments do
    resources :currency_accounts, only: [:show]
    resources :clinical_centers, only: [] do
      scope module: :clinical_centers do
        resources :notes, only: [:index]
        resources :investigators, only: [:index]
      end
    end
    resources :payments_approvals, only: [:create]
    resource :payments_awaiting_approval, only: [:show]
    resources :payments, only: [] do
      resources :invoice_uploads, only: [:new, :create], module: :payments
    end
    resources :notes, only: [:new, :create, :show, :index] do
      scope module: :notes do
        resources :payment_assignments, only: [:new, :create]
        resources :approvals, only: [:new, :create]
      end
    end
    resource :summary, only: [:show]
    resource :project_summary, only: [:show]
    resource :site_summary, only: [:show]
    resources :researchers, only: [:show] do
      resources :payments, only: [:index], module: :researchers
    end
  end

  namespace :v2 do
    resource :set_visit_date, only: [:create]
    namespace :sponsor do
      resources :operator_helpdesk_emails, only: [:new, :create] do
        collection do
          get :researcher_search
          get :researcher_details
        end
      end
      resources :note_corrections, only: [:index]
      resources :project_return_funds_notes, only: [:index]
      namespace :site_reviews do
        resources :sponsors, only: [:index] do
          scope module: :sponsors do
            resources :reviews, only: [:new, :create]
          end
        end
      end

      resources :project_invitations, only: [:new, :create]
      resources :simple_action_items, only: %i[new create]
      resources :action_items do
        scope module: :action_items do
          resources :responses, only: %i[new create]
          resource :resolve, only: %i[new create]
        end
      end

      resources :action_item_messages do
        scope module: :action_item_messages do
          resources :responses, only: %i[new create index]
          resources :processed_markings, only: %i[create]
        end
      end
      resource :search, only: %i[new show]
      resource :access_code, only: [:show]
      resources :cros, only: [:index, :show]
      namespace :payclinical_employees do
        resources :missing_forms, only: [:index]
        resources :user_action_requirements, only: [:index]
        resources :user_action_responses, only: [:index]
        resources :tasks
        resources :helpdesk_email_calls, only: %i[edit update]
        resources :project_codes, only: %i[edit update]
        resources :researchers, only: %i[show]
        resources :patient_data_acceptances, only: %i[new create]
        resources :researchers, only: [] do
          scope module: :researchers do
            resources :project_roles
          end
        end
        resource :search, only: %i[new show]
      end
      resources :calls_and_meetings do
        member do
          get :send_cm_email_form
          post :send_cm_email
          put :set_status
          get :agenda
          get :minutes
        end
      end

      resources :researcher_invitations, only: %i[edit update]
      resources :invoices, only: %i[index show]
      resources :pending_data_forms, only: [:index]
      resources :shipments, only: %i[index new create]
      resources :clinical_center_role_deactivations, only: [:new]
      resources :user_action_requirements, only: [:index] do
        post :set_as_pending
        post :set_as_resolved
      end
      resource :manage_access_rights, only: %i[new create show]
      resources :visit_payment_categorizations, only: [] do
        scope module: :visit_payment_categorizations do
          resource :document, only: [:show]
        end
      end
      resources :tos_versions, only: [:show] do
        collection do
          get 'current'
        end
      end
      resources :demo_project_roles, only: [:destroy]
      resources :clinical_centers do
        scope module: :clinical_centers do
          resources :comments, only: [:create]
          resources :clinical_center_closures, only: %i[new create]
          resources :clinical_center_openings, only: [:create]
          resources :clinical_users, only: %i[index new create]
          resources :clinical_user_account_activities, only: [:index]
          resources :patients_forms, only: [:index]
          resources :clinical_center_messages
          resource :payment_confirmations, only: %i[new create]
          resource :default_show_acc_toggle, only: [:update]
          resources :user_action_requirements, only: %i[index new create] do
            resources :user_action_responses, only: %i[new create index]
          end
          resources :activations, only: %i[new create]
        end
      end
      resources :visit_templates, only: %i[edit update]
      resource :disable_transfer_sent_confirmation_email, only: [:show]
      resources :researcher_manuals, only: %i[index show]
      resources :change_logs, only: [:index]
      resources :reminders, only: [:index]
      resources :project_roles, only: [:index]
      resource :account_history, only: [:show]
      resources :cro, only: [:show]
      resources :patient_forms, only: %i[show new create]
      resource :multi_currency_projects, only: [:show] do
        scope module: :multi_currency_projects do
          resources :clinical_centers, only: %i[new create]
          resources :clinical_users, only: [:index]
          resources :user_action_requirements, only: [:index]
          resources :researchers, only: [:index]
          resources :shipments, only: %i[index new create]
        end
      end
      resources :projects, only: %i[index show new create update] do
        scope module: :projects do
          resources :shipments
          resource :payment_confirmations, only: %i[new create]
          resources :clinical_centers, only: %i[index show new create]
          resources :clinical_users, only: %i[index new create]
          resources :researchers, only: %i[index new create] do
            get :in_site, on: :collection
          end
          resources :clinical_transfers, only: [:index]
          resources :user_action_requirements, only: [:index]
          resources :visit_payment_categories do
            put :update_multiple, on: :collection
          end
          resource :history, only: [:show]
          resources :patient_forms, only: %i[index show]
          resource :project_closures, only: %i[new create]
          resources :project_visit_templates
          resources :project_debits, only: [:index] do
            scope module: :project_debits do
              resource :pdfs, only: [:show]
            end
          end
          resources :project_debit_summaries, only: [:index] do
            scope module: :project_debit_summaries do
              resource :pdfs, only: [:show]
              resource :credit, only: [:new]
            end
          end
          resources :account_activity_logs, only: [:index]
          resources :statement_of_account, only: %i[index show]
          resource :complete_statement_of_account, only: %i[new create]
          resource :specified_period_statement_of_account, only: %i[new create]
          resource :add_funds, only: [:new, :create]
        end
      end

      resources :clinical_users do
        get :send_password, on: :member
        scope module: :clinical_users do
          resource :operator_access, only: [:new] do
            scope module: :operator_accesses do
              resource :data_entry, only: [:new, :create]
            end
          end
          resources :connected_accounts, only: [:index, :new, :create]
          resources :comments, only: %i[create edit update destroy]
          resource :verification, only: %i[new create]
          resource :password, only: %i[new create show]
          resource :data_verification, only: %i[new update]
          resource :visit_template_setting, only: %i[edit update]
          resource :suspension, only: %i[update new]
          resource :reactivation, only: %i[update new]
          resource :bank_account_number_validation, only: %i[new create]
          resources :clinical_user_forms, only: %i[new create]
          resources :clinical_user_account_activities, only: [:index]
          resources :clinical_transfers, only: [:index]
          resources :settings, only: [:index]
          resources :clinical_mobile_messages, only: [:index]
          resources :deactivations, only: %i[new create]
          resources :user_action_requirements, only: %i[new create] do
            resources :user_action_responses, only: %i[index new create]
          end
          resources :visits do
            put :toggle_high_priority, on: :member
            put :toggle_not_applicable, on: :member
            put :reset, on: :member
            scope module: :visits do
              resources :visit_payments, only: %i[new create]
            end
          end
        end
      end

      resources :visits, only: [] do
        collection do
          get :count_js_data
        end
        scope module: :visits do
          resources :clinical_transfers_acceptances, only: %i[create new]
          resources :clinical_transfers_rejections, only: %i[create new]
          resources :clinical_user_account_activities, only: [:index]
          resources :reminders, only: [:create]
          resource :previous_costs, only: :show
        end
      end

      resources :helpdesk_emails do
        resources :vip_upgrades, only: [:create], module: :helpdesk_emails
        resources :responses, only: [:new, :create], module: :helpdesk_emails
        resources :cancellations, only: [:new, :create], module: :helpdesk_emails
        resources :comments, only: [:new, :create, :edit, :update, :destroy], module: :helpdesk_emails
        resource :resolve, only: [:new, :create], module: :helpdesk_emails
        resource :resolve_silently, only: [:new, :create], module: :helpdesk_emails
        resources :reassignments, only: [:new, :create], module: :helpdesk_emails

        resources :helpdesk_email_tickets, only: %i[new create]
        resources :helpdesk_email_escalations, only: %i[new create]
        resources :helpdesk_email_shipments, only: %i[new create]
        resources :status_changes, only: %i[new create], module: :helpdesk_emails

        member do
          post :reject
          put :toggle_priority
        end
      end

      resources :messages do
        resources :message_responses, only: %i[new create]
        scope module: :messages do
          resource :clone, only: %i[new create]
        end
      end
      resource :outbox, only: [:show]

      # resources :project_debits, only: [:index] do
      #   # resources :project_debit_signatures, only: [:index, :edit, :update], param: :signature_hash
      # end

      resources :project_debit_summaries, only: %i[show index] do
        get :payment_confirmation, on: :member
        resource :po_edit, only: %i[new update]
        resources :cancellations, only: [:new, :create], module: :project_debit_summaries
      end

      resource :researchers, only: %i[edit update] do
        put :generate_default_avatar
        get :turn_off_transfer_waiting_approval_emails
      end
      resource :researcher_passwords, only: %i[edit update]
      resources :clinical_center_roles, only: [:destroy]
      resources :visit_revertings, only: %i[new create]
      resources :project_debit_signatures
      resource :notifications_settings, only: %i[show update]
      resource :email_notifications_settings, only: %i[show update]
      resource :sms_notifications_settings, only: %i[show update]
      resource :account_lock_and_password_change, only: [:new]
      resource :account_unlock_and_password_change, only: [:new]
      resource :account_lock, only: %i[new create]
      resource :account_unlock, only: %i[new create]

      resources :clinical_transfers, only: %i[show index] do
        scope module: :clinical_transfers do
          resources :clinical_transfers_cancellations, only: [:create]
          resources :clinical_transfers_acceptances, only: [:create]
          resources :clinical_transfers_rejections, only: [:create]
          resources :authorized_researchers, only: [:index]
          resource :confirmation, only: [:show]
          resource :destination, only: %i[edit update]
        end
      end
      resources :clinical_transfers_mass_acceptances, only: [:create]

      resources :researchers, only: %i[new create show edit update] do
        get :roles, on: :member
        scope module: :researchers do
          resources :unlockings, only: [:create]
          resource :reactivation, only: [:create] do
            get :reactivate_from_email, on: :member
          end
          resource :access_rights_notifications, only: [:create]
          resources :password_changes, only: [:create]
          resources :invitation_reminders, only: [:create]
          resources :invitation_removals, only: [:create]
          resources :roles_in_common_projects
          resource :researcher_blocking, only: %i[new create]
          resources :session_histories, only: [:index]
          resources :password_sendings, only: [:create]
          resources :project_roles, only: %i[index edit update show destroy] do
            scope module: :project_roles do
              resources :clinical_center_roles, only: [:create]
            end
          end
        end
      end
      resources :clinical_center_role_toggles, only: [:create]
      resources :project_debits do
        get :download_pds_pdf_document, on: :member
        get :send_reminder, on: :member
        resources :signatures_cancelations, only: [:create]
        scope module: :project_debits do
          resource :reset, only: [:create]
        end
      end
      resources :clinical_users, only: [:index]
    end

    ### SUBJECT ###
    namespace :subject do
      resource :clinical_users, only: %i[edit update] do
        get :clinical_center
      end
      resource :password_changes, only: %i[new update]
      resources :clinical_transfers, only: %i[index show]
      resources :clinical_transfers_pdf, only: [:index]
      resources :clinical_transfers_excel, only: [:index]
      resources :visits, only: %i[index show update] do
        scope module: :visits do
          resources :reminders, only: [:create]
        end
      end
      resources :notifications, only: [:index]
      resources :session_histories, only: [:index]
      resources :activity_logs, only: [:index]
      resource :email_notifications_settings, only: %i[edit update] do
        get :deactivate
      end
      resource :sms_notifications_settings, only: %i[edit update]
      resource :notifications_settings, only: %i[edit update]
    end
  end

  resource :home, only: [:index], controller: 'home' do
    collection do
      get :about
      get :services
      get :contact
      get :pricing
      post :pricing_calculator_result
    end
  end

  resource :contact_emails, only: [:create]
  resource :helpdesk_emails, only: [:create] do
    post :email_parser_error
  end

  begin
    ActiveAdmin.routes(self)
  rescue ActiveAdmin::DatabaseHitDuringLoad => e
    raise e unless Rails.env.local?
  end

  devise_for :admin_users, ActiveAdmin::Devise.config
  namespace :admin do
    resource :logged_in_user, only: [:show]
    resource :citi_file_status, only: [:show]
    resources :project_visit_templates
    resources :projects do
      resources :clinical_users
      resources :visit_types
      resources :invoices
      resources :fees
      resources :clinical_centers
    end
    resources :clinical_centers do
      resources :clinical_users
    end
    resources :simp_files do
      resources :clinical_transfers
      resources :project_debits
    end
    resources :project_debits do
      resources :clinical_transfers
      resources :simp_files
      resources :clinical_transfers
      resources :project_debit_signatures
    end
    resources :researchers do
      resources :clinical_transfers
    end
    resources :contract_research_organizations do
      resources :invoices
    end
    resources :clinical_users do
      resources :user_action_requirements
    end
    resources :citi_files do
      resources :clinical_transfers
    end

    resources :project_debit_summary_manual_cros, only: [:show], controller: :project_debit_summaries
  end

  devise_for :researcher, controllers: {
    sessions: 'researchers/sessions',
    passwords: 'researchers/passwords',
    registrations: 'researchers/registrations'
  }
  devise_scope :researcher do
    get '/researchers/:id/passwords/reset', to:'researchers/passwords#reset', as: :reset_researcher_password
  end

  get 'sponsor/researchers_invitations/edit' => 'researchers_invitations#edit'

  get 'sponsor/researchers_invitations/:id/update' => 'researchers_invitations#update',
  as: :update_researcher_invitation

  devise_for :clinical_users, controllers: {
    sessions: 'clinical_users/sessions',
    passwords: 'clinical_users/passwords'
  }

  authenticated :clinical_user do
    root to: 'v2/subject/clinical_users#edit', as: :authenticated_clinical_user_root
  end

  authenticated :researcher do
    root to: 'v2/sponsor/projects#index', as: :authenticated_researcher_root
  end
  root to: 'home#index', as: 'unauthenticated'
  delete 'admin/correct_closed_project_debit/:visit_id/withdraw_paid_visit' => 'correct_closed_project_debit#withdraw_paid_visit', as: :withdraw_paid_visit_from_closed_pd

  post '/zadarma/webhooks/724yhsan1290pwer', to: 'zadarma_webhooks#create', as: :zadarma_webhooks

  get '/:locale', to: 'home#index', locale: /en|pl/
end