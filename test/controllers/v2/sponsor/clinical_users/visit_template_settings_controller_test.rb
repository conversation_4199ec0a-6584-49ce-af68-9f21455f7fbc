require 'test_helper'

class VisitTemplateSettingsControllerTest < ControllerTestCase
  setup do
    @clinical_user = clinical_users(:clinical_user)
    @clinical_user.update_columns account_number: '33116022020000000034906630'
    @researcher = researchers(:researcher)
    sign_in @researcher
  end

  test "UPDATE" do
    pvt_id = project_visit_templates(:project_visit_template).id

    assert_changes '@clinical_user.reload.project_visit_template_id', to: pvt_id do
      put v2_sponsor_clinical_user_visit_template_setting_path(
        @clinical_user,
        clinical_user: {
          project_visit_template_id: pvt_id
        }
      )
    end
  end

  test 'EDIT' do
    get edit_v2_sponsor_clinical_user_visit_template_setting_path(@clinical_user)

    assert_response :success
  end
end
