require 'test_helper'

class V2::Sponsor::SmsNotificationsSettingsControllerTest < ControllerTestCase
  setup do
    @researcher = researchers(:researcher)
    sign_in @researcher
  end

  test "UPDATE" do
    assert_changes '@researcher.reload.failed_login_sms_notification', to: false do
      put v2_sponsor_sms_notifications_settings_path(
        @researcher,
        researcher: {
          failed_login_sms_notification: false
        }
      )
    end
  end
end
