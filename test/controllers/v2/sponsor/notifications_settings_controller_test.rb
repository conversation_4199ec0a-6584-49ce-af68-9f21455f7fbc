require 'test_helper'

class V2::Sponsor::NotificationsSettingsControllerTest < ControllerTestCase
  test "UPDATE" do
    researcher = researchers(:researcher)
    researcher.update_columns show_predicted_costs: false

    sign_in researcher

    assert_changes 'researcher.reload.show_predicted_costs', to: true do
      put v2_sponsor_notifications_settings_path(
        researcher: {
          show_predicted_costs: true,
          failed_login_notification_allowed: true,
          different_ip_notification_allowed: true,
          new_patient_notifications: true,
          receive_transfer_sent_confirmation_email: true,
          receive_waiting_transfers_email: true,
          site_payments_payment_notifications: true,
          failed_login_sms_notification: true
        }
      )
      assert_response :redirect
    end
  end
end
