require 'test_helper'

class ZadarmaWebhooksControllerTest < ActionDispatch::IntegrationTest
  test 'create' do
    call_start = Time.now

    assert_difference 'ZadarmaEvent.count' do
      post zadarma_webhooks_path, params: {
        event: 'event',
        call_start: call_start,
        pbx_call_id: 'pbx_call_id',
        caller_id: 'caller_id',
        called_did: 'called_did'
      }
    end

    assert_response :ok

    ze = ZadarmaEvent.last
    assert_equal 'event', ze.event
    assert_in_delta call_start, ze.call_start, 1
    assert_equal 'pbx_call_id', ze.pbx_call_number
    assert_equal 'caller_id', ze.caller_number
    assert_equal 'called_did', ze.called_number
  end

  test 'create without event' do
    assert_no_difference 'ZadarmaEvent.count' do
      post zadarma_webhooks_path, params: {
        call_start: Time.now,
        pbx_call_id: 'pbx_call_id',
        caller_id: 'caller_id',
        called_did: 'called_did'
      }
    end

    assert_response :ok
  end
end
