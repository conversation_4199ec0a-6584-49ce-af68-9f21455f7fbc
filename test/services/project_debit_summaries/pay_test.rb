require 'test_helper'

class ProjectDebitSummaries::PayTest < ActiveSupport::TestCase
  setup do
    @cro_acc = currency_accounts(:cro_currency_account)
    @cro_acc.update_columns balance: 20_000
  end

  test "pay normal note" do
    note = project_debit_summaries(:pds)
    amount = note.balance

    assert_difference 'InternalTransfer.count', 2 do
      assert_difference 'note.project.saldo', amount do
        assert_no_difference 'note.currency_account.balance' do
          assert_changes 'note.state', to: ProjectDebitSummary::PAID do
            assert_difference 'note.project.currency_account.balance', amount do
              assert_difference '@cro_acc.reload.balance', -amount do
                assert_no_difference 'ClinicalTransfer.count' do
                  result = ProjectDebitSummaries::Pay.call(note: note, source_currency_account_id: @cro_acc.id, amount: 20_000)

                  assert_equal amount.abs, result[:amount_used]
                end
              end
            end
          end
        end
      end
    end
  end

  test "pay normal note - not full amount" do
    note = project_debit_summaries(:pds)
    amount = note.balance - 10

    assert_difference 'InternalTransfer.count', 2 do
      assert_difference 'note.project.saldo', amount do
        assert_no_difference 'note.currency_account.balance' do
          assert_no_changes 'note.state' do
            assert_difference 'note.project.currency_account.balance', amount do
              assert_difference '@cro_acc.reload.balance', -amount do
                assert_no_difference 'ClinicalTransfer.count' do
                  result = ProjectDebitSummaries::Pay.call(note: note, source_currency_account_id: @cro_acc.id, amount: amount)
                  assert_equal amount.to_s, result[:amount_used].to_s
                end
              end
            end
          end
        end
      end
    end
  end

  test "pay EUR note from EUR account - not full amount" do
    note = project_debit_summaries(:eur_pds)
    amount = 50
    cro_acc = currency_accounts(:cro_eur_currency_account)
    pd = project_debits(:signed_eur_project_debit)
    transfer = clinical_transfers(:signed_eur_debit_waiting_transfer)
    transfer.update_columns amount: amount

    assert_difference 'note.reload.saldo', amount do
      assert_difference 'pd.reload.saldo', 100 do
        assert_changes 'transfer.reload.state' do
          result = ProjectDebitSummaries::Pay.call(note: note, source_currency_account_id: cro_acc.id, amount: amount)
        end
      end
    end
  end

  test "pay credit note" do
    note = project_debit_summaries(:unpaid_credit_pds)
    note.stubs(:balance).returns(100)
    note.stubs(:original_amount).returns(100)
    amount = note.balance
    cro_credit_acc = currency_accounts(:cro_credit_currency_account)

    InternalTransfer.create!(
      destination: note.currency_account,
      source: CurrencyAccount::ForCroCredit.where(resource: note.cro).first,
      amount: 100
    )

    assert_difference 'InternalTransfer.count', 2 do
      assert_difference 'note.project.saldo', 100 do
        assert_no_difference 'note.currency_account.balance' do
          assert_changes 'note.state', to: ProjectDebitSummary::PAID do
            assert_difference '@cro_acc.reload.balance', -amount do
              result = ProjectDebitSummaries::Pay.call(note: note, source_currency_account_id: @cro_acc.id, amount: 100)
              assert_equal amount.abs, result[:amount_used]
              assert_not_nil result[:bank_transfer]
            end
          end
        end
      end
    end
  end

  test 'new credit test' do
    note = project_debit_summaries(:prepaid_eur_pds)
    project = note.project
    project_acc = project.currency_account(note.currency)
    note_acc = note.currency_account
    eur_currency_account = currency_accounts(:eur_cro_credit_currency_account)
    cro_acc = currency_accounts(:eur_cro_currency_account)
    cro_credit_acc = currency_accounts(:eur_cro_credit_currency_account)

    ProjectDebitSummaries::Credit.call(currency_account: eur_currency_account, note: note, amount: 1000)
    note.reload

    assert_changes 'note.saldo' do
      assert_difference 'cro_acc.reload.balance', -10_000 do
        assert_difference 'InternalTransfer.where(source: cro_acc, destination: note_acc, amount: 10_000).count' do
          assert_difference 'ClinicalTransfer.where(amount: 1000, account_number: cro_credit_acc.account_number, source_acc_nr: eur_currency_account.account_number).count' do
            ProjectDebitSummaries::Pay.call(note: note, source_currency_account_id: cro_acc.id, amount: 10_000)
            assert_equal InternalTransfer.where(source: note_acc, destination: cro_credit_acc).last.amount, 5000
          end
        end
      end
    end
  end
end