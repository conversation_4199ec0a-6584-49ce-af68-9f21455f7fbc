require 'test_helper'

class SitePayments::Payment::StatesConcernTest < ActiveSupport::TestCase
  setup do
    @payment = site_payments_payments(:sp_payment)
  end

  test 'state_after_invoice_upload - by manager' do
    @payment.stubs(:added_by_role).returns('Manager')

    assert_equal 'processing', @payment.state_after_invoice_upload
  end

  test 'state_after_invoice_upload - not by manager' do
    @payment.stubs(:added_by_role).returns('CRA')

    assert_equal 'awaiting_approval', @payment.state_after_invoice_upload
  end

  test 'amount_within_project_limit?' do
    @payment.stubs(:added_by_role).returns('Investigator')
    @payment.amount = 100
    project = @payment.project
    project.investigator_transfer_limit = 10

    assert_not @payment.amount_within_project_limit?

    @payment.amount = 10
    assert @payment.amount_within_project_limit?
  end

  test 'added_by_role' do
    @payment.added_by_researcher = researchers(:operator)
    assert 'Operator', @payment.added_by_role
  end

  test 'accept - can be accepted' do
    researcher = researchers(:researcher)

    assert_changes '@payment.accepted_by', to: researcher do
      assert_changes '@payment.accepted_at' do
        assert_changes '@payment.reload.state', to: 'processing' do
          @payment.accept(acceptor: researcher)
        end
      end
    end
  end

  test 'accept - cannot be accepted' do
    @payment.stubs(:can_be_accepted_by?).returns(false)

    assert_no_changes '@payment.reload.state' do
      @payment.accept(acceptor: researchers(:researcher))
      assert @payment.valid?
    end
  end

  test 'can_be_accepted_by?' do
    assert @payment.can_be_accepted_by?(acceptor: researchers(:researcher))
    assert_not @payment.can_be_accepted_by?(acceptor: researchers(:operator))

    @payment.stubs(:sp_manager_max_payment_limit).returns(1)

    assert_not @payment.can_be_accepted_by?(acceptor: researchers(:researcher))
    assert_not @payment.can_be_accepted_by?(acceptor: researchers(:operator))
  end

  test 'amount_over_limit?' do
    @payment.amount = 100

    @payment.stubs(:sp_manager_max_payment_limit).returns(nil)
    assert_not @payment.amount_over_limit?

    @payment.stubs(:sp_manager_max_payment_limit).returns(1)
    assert @payment.amount_over_limit?

    @payment.stubs(:sp_manager_max_payment_limit).returns(1000)
    assert_not @payment.amount_over_limit?
  end
end
