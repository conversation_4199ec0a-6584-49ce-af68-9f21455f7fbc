require 'test_helper'

class ActionItemTest < ActiveSupport::TestCase
  setup do
    @researcher = researchers(:researcher)
    @operator = researchers(:operator)
    @ai = action_items(:site_action_item)
  end

  test 'pending?' do
    pending_ai = ActionItem.new(status: 'pending', created_at: Time.new)
    lvl1_ai = ActionItem.new(status: 'pending', created_at: 8.days.ago)

    assert pending_ai.pending?
    assert_not lvl1_ai.pending?
  end

  test 'lvl_1_escalation?' do
    pending_ai = ActionItem.new(status: 'pending', created_at: Time.new)
    lvl1_ai = ActionItem.new(status: 'pending', created_at: 8.days.ago)

    assert_not pending_ai.lvl_1_escalation?
    assert lvl1_ai.lvl_1_escalation?
  end

  test 'lvl_2_escalation?' do
    pending_ai = ActionItem.new(status: 'pending', created_at: Time.new)
    lvl1_ai = ActionItem.new(status: 'pending', created_at: 8.days.ago)
    lvl2_ai = ActionItem.new(status: 'pending', created_at: 15.days.ago)

    assert_not pending_ai.lvl_2_escalation?
    assert_not lvl1_ai.lvl_2_escalation?
    assert lvl2_ai.lvl_2_escalation?
  end

  test 'for_source' do
    assert_includes ActionItem.for_source(projects(:project)), action_items(:site_action_item)
    assert_includes ActionItem.for_source(projects(:project)), action_items(:patient_action_item)
    assert_not_includes ActionItem.for_source(projects(:project)), action_items(:premium_subject_with_visit_ai)

    assert_includes ActionItem.for_source(contract_research_organizations(:cro)), action_items(:site_action_item)
    assert_includes ActionItem.for_source(contract_research_organizations(:cro)), action_items(:patient_action_item)
    assert_not_includes ActionItem.for_source(contract_research_organizations(:cro)), action_items(:premium_subject_with_visit_ai)
  end

  test 'first_escalatee' do
    ActionItems::Escalations::First.expects(:call)

    @ai.first_escalatee
  end

  test 'second_escalatee' do
    ActionItems::Escalations::Second.expects(:call)

    @ai.second_escalatee
  end

  test 'set_number' do
    ai = action_items(:site_action_item)
    nr = '123'

    ActionItems::GenerateNumber.expects(:call).with(action_item: ai).returns(nr)

    assert_changes 'ai.reload.number', to: nr do
      ai.set_number
    end
  end

  test 'set_number after creating' do
    ai = ActionItem.new

    ai.expects(:set_number)

    ai.save!(validate: false)
  end

  test 'for_researcher' do
    assert_includes ActionItem.for_researcher(@researcher).ids, @ai.id
    assert_includes ActionItem.for_researcher(@operator).ids, @ai.id
    assert_not_includes ActionItem.for_researcher(researchers(:jack)).ids, @ai.id
  end
end
