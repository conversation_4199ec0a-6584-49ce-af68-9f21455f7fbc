require "test_helper"

class FeePlanTest < ActiveSupport::TestCase
  test '#rule_active?' do
    assert fee_plans(:premium).rule_active?(fee_plan_rules(:upload_clinical_user_forms))
    refute fee_plans(:free).rule_active?(fee_plan_rules(:upload_clinical_user_forms))
  end

  test '#formatted_processing_message' do
    project = Project.new(clinical_protocol_code: '123')
    plan = fee_plans(:free)

    assert_equal "Processing message for 123 - Limited Support", plan.formatted_processing_message(project)

    plan.processing_message = nil
    assert_nil plan.formatted_processing_message(project)
  end
end
