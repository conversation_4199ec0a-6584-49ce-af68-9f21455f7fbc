require 'application_system_test_case'

class ResearcherActivationTest < NoJsSystemTestCase
  test 'Researcher activates account with correct token' do
    token = '123abv'
    r = researchers(:researcher)
    r.update_columns reset_password_token: token, reset_password_sent_at: 1.minute.ago

    visit sponsor_researchers_invitations_edit_path(reset_password_token: token)

    password = 'Password123!'

    fill_in 'researcher_password', with: password
    fill_in 'researcher_password_confirmation', with: password

    check :researcher_tos

    click_button 'Activate'

    assert_selector('body.authenticated')
    assert r.reload.valid_password?(password)
  end
end