require 'application_system_test_case'

module Admin
  class ClinicalTransfersTest < NoJsSystemTestCase
    include ActionMailer::TestHelper

    test 'send reason request' do
      sign_in admin_users(:admin)
      transfer = clinical_transfers(:transfer_to_cro_currency_account)

      visit admin_clinical_transfer_path(transfer)
      click_link 'Wyslij prosbe o specyfikacje >>'
      click_link 'Wyślij'
      # emails dont register but shows in log
    end

    test 'generate refunds' do
      sign_in admin_users(:admin)
      visit admin_clinical_transfers_path

      assert_difference 'CitiFile.count' do
        click_link 'Wygeneruj zwroty srodkow'
        assert_text 'Plik jest generowany. Pobierz go za chwilę.'
        assert_equal ClinicalTransfer::SENT_TO_BANK, clinical_transfers(:currency_account_refund_for_processing).reload.state
      end
    end
  end
end
