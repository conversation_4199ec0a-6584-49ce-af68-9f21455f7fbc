require 'application_system_test_case'

class Admin::SqlReportsTest < NoJsSystemTestCase
  test 'preview sql outcome' do
    sign_in admin_users(:admin)

    visit new_admin_sql_report_path
    fill_in 'Sql', with: 'SELECT clinical_protocol_code as study_protocol FROM "projects" WHERE "projects"."contract_research_organization_id" = {{cro_id}} LIMIT {{limit}}', match: :first
    click_button 'Preview'

    assert_text 'Params missing: cro_id, limit'

    fill_in 'Sql params', with: "cro_id: #{contract_research_organizations(:cro).id}, limit: 3"
    click_button 'Preview'

    within '#sql_preview' do
      assert_text 'study_protocol'
      assert_text projects(:project).clinical_protocol_code
    end
    assert_selector '.sql_result_row', count: 3

    fill_in 'Name', with: 'Report1'
    click_button 'Save'

    assert_current_path admin_sql_reports_path
    assert_text 'Report1'
  end

  test 'update report' do
    sign_in admin_users(:admin)
    visit edit_admin_sql_report_path(sql_reports(:select_report))
    fill_in 'Name', with: 'New Report'
    click_button 'Save'

    assert_current_path admin_sql_report_path(sql_reports(:select_report))
    assert_text 'New Report'
    assert_no_text 'Select Report'
  end

  test 'show errors' do
    sign_in admin_users(:admin)

    visit new_admin_sql_report_path
    fill_in 'Sql', with: 'SELECT damnit', match: :first
    click_button 'Preview'
    assert_text 'PG::UndefinedColumn: ERROR: column "damnit" does not exist LINE 1: SELECT damnit'
  end
end
