require 'application_system_test_case'

class V2::Sponsor::ClinicalCenters::ReviewsTest < NoJsSystemTestCase
  setup do
    @operator = researchers(:operator)
    @cro = contract_research_organizations(:cro)
    @cro.update_columns importance: 'vip'
    @premium_cro = contract_research_organizations(:premium_cro)
    @premium_project = projects(:premium_project)
    @site = clinical_centers(:premium_project_site)
  end

  test 'operator reviews sites and AIs' do
    clinical_users(:premium_parent_with_visit).destroy
    clinical_users(:premium_subject_with_visit).update_columns clinical_center_id: nil
    ai = action_items(:premium_project_site_action_item)
    rec = recommendations(:recommendation)

    sign_in @operator
    visit root_path
    click_link 'Site review'

    assert_text @premium_cro.name
    assert_text @cro.name

    within "tr#cro_#{ @premium_cro.id }" do
      click_link 'Start review'
    end

    assert_text @premium_project.clinical_protocol_code
    check "ai_review_#{ ai.id }"
    check "ai_remind_#{ ai.id }"
    select rec.body, from: "recommendations[#{ @site.id }]"

    assert_difference '@site.site_reviews.count' do
      assert_difference 'ai.ai_reviews.count' do
        assert_difference 'ai.notifications.count' do
          assert_difference 'ReviewRecommendation.count' do
            click_button 'Confirm review & go to next study'

            assert_equal ActionItem::Notification.last.review, Review.last
          end
        end
      end
    end
  end

  test 'skip to next site' do
    sign_in @operator
    visit root_path
    click_link 'Site review'

    within "tr#cro_#{ @premium_cro.id }" do
      click_link 'Start review'
    end

    assert_no_difference 'SiteReview.count' do
      click_button 'Next study only'
    end
  end
end