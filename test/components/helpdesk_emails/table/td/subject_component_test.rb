require 'test_helper'

module HelpdeskEmails
  module Table
    module Td
      class SubjectComponentTest < ViewComponent::TestCase
        setup do
          @email = HelpdeskEmail.new(tag: 'vip', id: 1, created_at: Time.current)
        end

        test "show tag and action" do
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          action = 'action'
          component.stubs(:action).returns(action)

          render_inline(component)
          assert_text "VIP: #{ action }"
        end

        test "show plan name and action" do
          email = HelpdeskEmail.new(tag: 'plan', id: 1)
          plan_name = 'Silver'
          email.expects(:fee_plan_name_only).returns(plan_name)

          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: email)
          action = 'action'
          component.stubs(:action).returns(action)
          component.stubs(:overdue?).returns(true)

          render_inline(component)
          assert_text "#{ plan_name.upcase }: #{ action }"
        end

        test "no response sent action" do
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          @email.status = 'pending'
          day = 'today'
          component.stubs(:resolve_till).returns(day)

          assert_equal component.action, "Resolve till #{ day }"
        end

        test "no response sent overdue action" do
          @email.status = 'pending'
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          component.stubs(:resolve_deadline).returns(3.days.ago.to_date)

          assert_includes component.action, "overdue"
        end

        test "not checked in action" do
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          component.stubs(:waiting_for_check_in_time).returns('3 days')

          assert_includes component.action, "Waiting 3 days for check-in"
        end

        test 'resolved action' do
          @email.status = 'resolved'
          date = Time.current
          @email.resolved_at = date
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)

          assert_equal component.action, "Resolved on #{ I18n.l date }"
        end

        test "resolve_deadline for super_premium" do
          @email.expects(:fee_plan).returns('super_premium')
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)

          assert_equal component.resolve_deadline, Date.today
        end

        test "resolve_deadline for other plans" do
          @email.expects(:fee_plan).returns('premium')
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)

          assert_equal component.resolve_deadline, Date.tomorrow
        end

        test "resolve_till" do
          travel_to Time.new(2025, 4, 24, 12, 0, 0)

          I18n.with_locale :en do
            component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)

            component.stubs(:resolve_deadline).returns(Date.today)
            assert_equal 'today', component.resolve_till

            component.stubs(:resolve_deadline).returns(Date.tomorrow)
            assert_equal 'tomorrow', component.resolve_till

            component.stubs(:resolve_deadline).returns(Date.yesterday)
            component.stubs(:created_at).returns(Date.yesterday)
            assert_includes component.resolve_till, '1 day overdue'
          end
        end

        test "response sent action" do
          @email.status = 'pending'
          response = HelpdeskResponse.new(created_at: Time.new)

          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          component.stubs(:response).returns(response)

          assert_equal component.action, "Response sent on #{ I18n.l response.created_at, format: :only_date_dash }"
        end

        test "pre_checkin_action_class" do
          @email.status = 'new'

          @email.created_at = 7.hours.ago
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          assert_equal component.pre_checkin_action_class, "red"

          @email.created_at = 2.hours.ago
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          assert_equal nil, component.pre_checkin_action_class
        end

        test 'tag_class' do
          @email.tag = 'vip'
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          assert_equal 'red', component.tag_class

          @email.tag = 'plan'
          @email.expects(:free_plan?).returns(true)
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          assert_equal nil, component.tag_class

          @email.tag = 'plan'
          @email.expects(:free_plan?).returns(false)
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          assert_equal 'red', component.tag_class
        end

        test 'link class' do
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          component.stubs(:overdue?).returns(false)

          assert_equal 'hover_orange', component.link_class
        end

        test 'status new, less than 30 mins after creation' do
          @email.status = 'new'
          @email.created_at = 29.minutes.ago
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          assert_equal '<span>1 min left to check-in</span>', component.action_with_time_info
        end

        test 'status new, more than 30 mins after creation' do
          @email.status = 'new'
          @email.created_at = 35.minutes.ago
          component = HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: @email)
          assert_equal '<span class="text-danger">-5 min left to check-in</span>', component.action_with_time_info
        end
      end
    end
  end
end