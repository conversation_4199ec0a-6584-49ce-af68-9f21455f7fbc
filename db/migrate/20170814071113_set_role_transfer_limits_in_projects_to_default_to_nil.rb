class SetRoleTransferLimitsInProjectsToDefaultToNil < ActiveRecord::Migration[4.2]
  def up
    change_column_default(:projects, :investigator_transfer_limit, nil)
    change_column_default(:projects, :cra_transfer_limit, nil)
    change_column_default(:projects, :manager_transfer_limit, nil)
  end

  def down
    change_column_default(:projects, :investigator_transfer_limit, 0)
    change_column_default(:projects, :cra_transfer_limit, 0)
    change_column_default(:projects, :manager_transfer_limit, 0)
  end
end
