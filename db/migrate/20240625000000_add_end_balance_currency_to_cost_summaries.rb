class AddEndBalanceCurrencyToCostSummaries < ActiveRecord::Migration[6.0]
  def change
    add_column :cost_summaries, :end_balance_currency, :string, default: 'PLN'

    # Update existing cost summaries to use their project's currency
    execute <<-SQL
      UPDATE cost_summaries
      SET end_balance_currency = COALESCE((SELECT currency FROM projects WHERE projects.id = cost_summaries.project_id), 'PLN')
    SQL
  end
end
