class SortVisitPaymentCategorizations < ActiveRecord::Migration[4.2]
  def up
    unless VisitPaymentCategory.count == 0
      tx = VisitPaymentCategory.where(abbr: "TX").first
      tx.position = 1 if tx
      tr = VisitPaymentCategory.where(abbr: "TR").first
      tr.position = 4 if tr
      tb = VisitPaymentCategory.where(abbr: "TB").first
      tb.position = 6 if tb
      to = VisitPaymentCategory.where(abbr: "TO").first
      to.position = 8 if to

      [tx,tr,tb,to].each { |x| x.try(:save!) }
    end
  end

  def down
  end
end
