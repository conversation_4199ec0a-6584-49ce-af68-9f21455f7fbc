require "rails_helper"

RSpec.feature "Add clinical user", :type => :feature do
  let(:admin_user) { create :admin_user, role: 'admin' }
  let(:project) { create :project }
  let(:clinical_center) { create :clinical_center, project_id: project.id, account_number: '35102043910000650200461186' }
  let!(:clinical_user) { create :clinical_user, project: project, clinical_center: clinical_center, data_confirmed_at: nil, data_status: 'input_operator_to_accept' }

  scenario 'doesnt allow invalid sec code' do
    sign_in_matrix(admin_user)

    visit admin_clinicaluserstoverify_path
    click_link 'Zweryfikuj'
    fill_in 'clinical_user_bank_account_sec_code', with: '000000'
    click_button 'Zapisz uczestnika'

    expect(clinical_user.reload.data_confirmed_at).to be_nil


    fill_in 'clinical_user_bank_account_sec_code', with: clinical_user.valid_bank_account_sec_code
    click_button 'Zapisz uczestnika'

    expect(clinical_user.reload.data_confirmed_at).not_to be_nil
    expect(clinical_user.data_status).to eq 'input_operator_accepted'
  end

  scenario 'update transfers to bank' do
    clinical_user.update_column :transfer_destination, 'lmp_acc'
    ct = create :clinical_transfer, clinical_user: clinical_user, account_number: '', state: ClinicalTransfer::PROCESSING
    sign_in_matrix(admin_user)

    visit admin_clinicaluserstoverify_path
    click_link 'Zweryfikuj'

    fill_in 'clinical_user_bank_account_sec_code', with: clinical_user.valid_bank_account_sec_code
    select 'bank_account', from: 'clinical_user_transfer_destination'
    click_button 'Zapisz uczestnika'

    expect(ct.reload.account_number).to eq clinical_user.reload.account_number
  end
end
