require 'rails_helper'

RSpec.feature 'UserActionRequirements', type: :feature do
  let(:researcher) { create :researcher, payclinical_employee: true }

  scenario 'researcher can filter by protocol and subject' do
    uar1 = create :user_action_requirement, reason: 'uar1', researcher_id: researcher.id
    uar2 = create :user_action_requirement, reason: 'uar2'

    sign_in_researcher(researcher)
    visit v2_sponsor_payclinical_employees_user_action_requirements_path

    expect(page).to have_content uar1.reason
    expect(page).to have_content uar2.reason

    select uar1.clinical_protocol_code, from: :q_project_id_eq
    find('.submit_btn').click

    expect(page).to have_content uar1.reason
    expect(page).not_to have_content uar2.reason

    select 'Select Protocol', from: :q_project_id_eq
    select uar2.patient_code, from: :q_clinical_user_id_eq
    find('.submit_btn').click

    expect(page).not_to have_content uar1.reason
    expect(page).to have_content uar2.reason

    select 'Select Protocol', from: :q_project_id_eq
    select 'Select Subject', from: :q_clinical_user_id_eq
    select researcher.full_name, from: :q_researcher_id_eq
    find('.submit_btn').click
    expect(page).to have_content uar1.reason
    expect(page).to_not have_content uar2.reason

    select 'Select Protocol', from: :q_project_id_eq
    select 'Select Subject', from: :q_clinical_user_id_eq
    select 'Select Researcher', from: :q_researcher_id_eq
    fill_in :q_reason_cont, with: 'ar1'
    find('.submit_btn').click
    expect(page).to have_content uar1.reason
    expect(page).to_not have_content uar2.reason
  end
end
