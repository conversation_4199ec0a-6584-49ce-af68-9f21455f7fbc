require 'rails_helper'
require 'rspec'

RSpec.describe VisitPaymentCategory do
  let(:vpc) { create :visit_payment_category }
  let(:cro) { create :contract_research_organization }
  let(:project) { create :project }

  it 'has a valid factory' do
    vpc = build :visit_payment_category
    expect(vpc).to be_valid
  end

  describe '#update_all_cro_vpc' do
    it 'creates a copy of itself belonging to cro' do
      cro
      vpc
      expect(cro.visit_payment_categories.count).to eq 1
      expect(cro.visit_payment_categories.first.base_visit_payment_category_id).to eq vpc.id
    end

    it 'doesnt create a copy of itself belonging to cro if cro has it already' do
      vpc
      cro
      vpc.update_all_cro_vpc
      expect(cro.visit_payment_categories.count).to eq 1
      expect(cro.visit_payment_categories.first.base_visit_payment_category_id).to eq vpc.id
    end
  end

  describe '#update_projects_belonging_cro' do
    it 'creates copies of itself owned by projects belonging to the owner cro' do
      project.contract_research_organization_id = cro.id
      project.save
      another_project = create :project

      vpc = create :visit_payment_category, owner: cro
      expect(project.visit_payment_categories.count).to eq 1
      expect(project.visit_payment_categories.first.base_visit_payment_category_id).to eq vpc.id
    end
  end

  describe '#update_child_categories' do
    it 'updates all related copied categories' do
      vpc = create :visit_payment_category, name_pl: 'taxi', name_en: 'taxi', position: 1, visible: false, abbr: 'tx'
      copy = create :visit_payment_category, base_visit_payment_category_id: vpc.id, name_pl: 'bus', name_en: 'bus', position: 10, visible: true, abbr: 'bs'
      vpc.update_child_categories
      expect(copy.reload).to satisfy do |copy|
        copy.name_pl == vpc.name_pl
        copy.name_en == vpc.name_en
        copy.abbr == vpc.abbr
        copy.position == vpc.position
        copy.visible == vpc.visible
      end
    end

    it 'doesnt update unrelated vpc' do
      vpc = create :visit_payment_category, name_pl: 'taxi', name_en: 'taxi', position: 1, visible: false, abbr: 'tx'
      unrelated_vpc = create :visit_payment_category
      expect { vpc.update_child_categories }.not_to change { unrelated_vpc }
    end
  end

  describe '#copies_and_base_vpc_ids' do
    it 'returns ids of base and copies vpc' do
      base = create :visit_payment_category, base_visit_payment_category_id: nil
      vpc = create :visit_payment_category, base_visit_payment_category_id: base.id
      copy = create :visit_payment_category, base_visit_payment_category_id: base.id
      expect(vpc.copies_and_base_vpc_ids).to include(base.id, vpc.id, copy.id)
    end
  end
end
