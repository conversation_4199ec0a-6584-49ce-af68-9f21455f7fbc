require 'rails_helper'

RSpec.describe ProjectDebitsCron do
  describe '#send_reminder_to_cras' do
    it 'sends email' do
      ActionMailer::Base.deliveries = []
      pds = create :project_debit_signature, file_path: 'fixturestest.pdf'

      expect(ResearcherMailer).to receive_message_chain(:with, :send_pds_remider_to_cra, :deliver)

      ProjectDebitsCron.send_reminder_to_cras(pds: [pds])
    end
  end

  describe '#gen_note_for_debits' do
    it 'call GenerateVisitActionsJob' do
      pr = create :project
      pd = create :project_debit, project: pr

      expect(GenerateVisitActionsJob).to receive(:perform_later).with(visits: pd.visits, action_name: 'unpaid_debit_note')

      ProjectDebitsCron.gen_note_for_debits(project: pr, project_debits: [pd], params: {})
    end
  end

  describe '.gen_quintiles_summary_debit_note_per_project' do
    it "calls gen_quintiles_summary_debit_note for each project" do
      pr = build(:project)
      expect(ProjectDebitsCron).to receive(:gen_quintiles_summary_debit_note).with(pr)

      ProjectDebitsCron.gen_quintiles_summary_debit_note_per_project(projects: [pr])
    end
  end

  describe ".gen_quintiles_summary_debit_note" do
    context "summary_per_debit" do
      it "generates one note per debit" do
        cro = create :contract_research_organization, summary_per_debit: true
        project = create :project, contract_research_organization: cro

        pd1 = build :project_debit
        pd2 = build :project_debit

        allow(ProjectDebitSummary).to receive(:find_ready_project_debits).with(project) { [pd1, pd2] }

        expect(ProjectDebitsCron).to receive(:gen_note_for_debits).with(params: {}, project: project, project_debits: pd1)
        expect(ProjectDebitsCron).to receive(:gen_note_for_debits).with(params: {}, project: project, project_debits: pd2)

        ProjectDebitsCron.gen_quintiles_summary_debit_note(project)
      end
    end
  end
end
