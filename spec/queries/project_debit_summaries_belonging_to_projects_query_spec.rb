require 'rails_helper'

RSpec.describe ProjectDebitSummariesBelongingToProjectsQuery do
  let(:project) { create :project }

  describe '#run' do
    it 'returns ProjectDebitSummary that belong to project directly and throug project_debits' do
      pds1 = create :project_debit_summary, project: project
      pds2 = create :project_debit_summary, project: nil
      project_debit = create :project_debit, project: project, project_debit_summary: pds2

      expect(ProjectDebitSummariesBelongingToProjectsQuery.run(project_ids: project.id)).to include(pds1, pds2)
    end
  end
end
