FactoryBot.define do
  factory :clinical_transfer do
    project
    amount { 10 }
    title { Faker::Lorem.word }
    state { 'w' }
    status_change_date { Time.now }
    researcher
    clinical_user
    flow_direction { 'o' }
    account_number { '33116022020000000034906630' }
    visit

    factory :sent_transfer do
      state { ClinicalTransfer::SENT_TO_BANK }
    end
  end



  factory :clinical_transfer_skips_validate do
    to_create {|instance| instance.save(validate: false) }
  end
end
