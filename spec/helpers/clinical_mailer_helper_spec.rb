require 'rails_helper'

RSpec.describe ClinicalMailerHelper  do
  describe '#text_without_double_dot_ending' do
    it 'removes a dot from the end if 2 dots at the end' do
      text = 'abc..'

      expect(helper.text_without_double_dot_ending(text)).to eq('abc.')
    end

    it 'returns text if doesnt end with 2 dots' do
      text = 'abc.'

      expect(helper.text_without_double_dot_ending(text)).to eq('abc.')

      text = 'abc'

      expect(helper.text_without_double_dot_ending(text)).to eq('abc')
    end
  end
end
