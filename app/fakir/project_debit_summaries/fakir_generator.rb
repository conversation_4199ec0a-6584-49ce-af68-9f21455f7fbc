module ProjectDebitSummaries::<PERSON>akirGenerator
  def self.generate_nag(summaries)
    summaries.map do |pds|
      "#{pds.id};0;#{pds.note_number};#{pds.created_at.strftime('%d-%m-%Y')};;;#{pds.created_at.strftime('%d-%m-%Y')};#{pds.fakir_opis_dokumentu};;0;N;NOTA;;0;"
    end.join("\n")
  end

  def self.generate_poz(summaries)
    summaries.map do |pds|
      ProjectDebitSummaries::GeneratePoz.call(note: pds)
    end.join("\n")
  end

  def self.generate_kontrah(summaries)
    summaries.group_by(&:contract_research_organization_id).map do |cro_id, summaries|
      inv = summaries.first
      "#{ inv.cro_country_code };#{ inv.cro_company_name };#{ inv.cro_company_name };1;1;#{ inv.cro_company_nip };;1;#{ inv.fakir_kod_kontrahenta };#{ inv.cro_company_zip_code };#{ inv.cro_company_city };#{ inv.cro_company_street };;;"
    end.join("\n")
  end
end