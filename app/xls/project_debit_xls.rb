class ProjectDebitXls
  attr_accessor :locale, :project_debit_ids, :book, :sheet_name

  def initialize(params = {})
    @locale = params.fetch :locale, 'pl'
    @project_debit_ids = params[:project_debit_ids]
    @sheet_name = params[:sheet_name]
  end

  def generate
    tab = [[ I18n.t('pds_xls.order_date'),
      I18n.t('pds_xls.center'),
      I18n.t('pds_xls.patient_code'),
      I18n.t('pds_xls.visit_name'),
      I18n.t('pds_xls.visit_date'),
      I18n.t('pds_xls.cost_by_cat'),
      I18n.t('pds_xls.cost_desc'),
      I18n.t('pds_xls.amount'),
      I18n.t('pds_xls.balance'),
      I18n.t('pds_xls.monitor_name'),
      'Status'
      ]]

      _current_saldo = 0
      sorted_transfers.each do |t|
        _current_saldo += t.amount unless t.cancelled?

        t_status = if t.cancelled?
          'Cancelled'
        elsif !t.waiting?
          'Confirmed'
        else
          ''
        end

        t.visit.visit_payment_categorizations.where('amount > 0').includes(:visit_payment_category).each { |x|
          cat_tmp = "#{x.try(:visit_payment_category).try(:abbr)}:#{"%.2f" % x.amount}"
          descr_tmp = "#{x.descr.gsub(/;H/,' km. Powyżej 900cm3.').gsub(/;L/,' km. Do 900cm3.')}" if x.descr.size > 0
          tab << [
           I18n.l(t.created_at, format: :only_date_slash),
           t.clinical_user.clinical_center.clinical_center_code,
           t.clinical_user.patient_code,
           t.visit.name,
           I18n.l(t.visit.visit_date, format: :only_date_slash),
           cat_tmp[0..-2],
           descr_tmp,
           "%.2f" % t.amount,
           "%.2f" % _current_saldo,
           t.researcher.full_name,
           t_status
         ]
       }
     end

     Researcher.transaction do
      @book = Spreadsheet::Workbook.new
      sheet = @book.create_worksheet name: sheet_name
      tab.each_with_index do |_row, index|
        sheet.row(index).replace _row
      end
    end
  end

  def sorted_transfers
    ClinicalTransfer.where(project_debit_id: project_debit_ids).joins(:visit).outgoing.sort_by { |t|
      [t.project_debit.full_note_number, t.clinical_user.clinical_center.clinical_center_code,
        t.created_at.to_date, t.clinical_user.patient_code, t.visit.visit_date]
      }
    end
  end
