class Alior::CsvReport::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def initialize(file:)
    @file = file
  end

  def summary_data
    incoming_count = 0
    incoming_amount = 0
    outgoing_count = 0
    outgoing_amount = 0

    amount_column = 7

    CSV.foreach(@file.path, headers: false).each do |row|
      if incoming?(row)
        incoming_count += 1
        incoming_amount += amount(row[amount_column])
      else
        outgoing_count += 1
        outgoing_amount += amount(row[amount_column])
      end
    end

    {
      incoming_count: incoming_count,
      incoming_amount: incoming_amount,
      outgoing_count: outgoing_count,
      outgoing_amount: outgoing_amount
    }
  end


  private

  def amount(amount_cell)
    amount_cell.to_d
  end

  def incoming?(row)
    row[0] == "1"
  end
end
