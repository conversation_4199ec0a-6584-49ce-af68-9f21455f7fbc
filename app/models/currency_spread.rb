# == Schema Information
#
# Table name: currency_spreads
#
#  id                :bigint           not null, primary key
#  base_currency     :string
#  currency          :string
#  internal_transfer :decimal(, )
#  invoice           :decimal(, )
#  note              :decimal(, )
#  resource_type     :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  resource_id       :bigint
#
# Indexes
#
#  index_currency_spreads_on_resource_type_and_resource_id  (resource_type,resource_id)
#
class CurrencySpread < ApplicationRecord
  belongs_to :resource, polymorphic: true

  validates :currency, :resource_id, :resource_type, :base_currency, presence: true
  validates :currency, :base_currency, inclusion: { in: Currency::AVAILABLE }

  def self.find_for_resource(resource:, target_currency:, base_currency:)
    result = resource.currency_spreads.where(currency: target_currency, base_currency: base_currency).order('id desc').first

    if result.nil? && resource.respond_to?(:contract_research_organization)
      result = resource.contract_research_organization.currency_spreads.where(currency: target_currency, base_currency: base_currency).order('id desc').first
    end

    result
  end

  def self.default_for_invoice
    LmpSetting.currency_spread_for_invoice
  end

  def self.default_for_note
    LmpSetting.currency_spread_for_note
  end

  def self.default_for_internal_transfer
    LmpSetting.currency_spread_for_internal_transfer
  end
end
