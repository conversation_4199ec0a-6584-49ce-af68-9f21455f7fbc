# == Schema Information
#
# Table name: action_requirement_notifications
#
#  id                         :integer          not null, primary key
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  sent_to_researcher_id      :integer
#  user_action_requirement_id :integer
#
# Indexes
#
#  index_arn_on_str_id  (sent_to_researcher_id)
#  index_arn_on_uar_id  (user_action_requirement_id)
#
class ActionRequirementNotification < ApplicationRecord
  belongs_to :user_action_requirement
  belongs_to :sent_to_researcher, class_name: 'Researcher', foreign_key: :sent_to_researcher_id
end
