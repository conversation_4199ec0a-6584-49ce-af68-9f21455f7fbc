class ClinicalCenterRole < ApplicationRecord
  belongs_to :clinical_center, touch: true
  belongs_to :project_role, touch: true


  ### CONSTANTS

  ROLES = [
  	'Manager'
  ]

  ### VALIDATIONS
  validates :clinical_center_id, presence: true
  validates :clinical_role, inclusion: { in: ROLES, allow_blank: true }
  validates :clinical_center_id, uniqueness: { scope: :project_role_id }

  delegate :project, :researcher, to: :project_role
  delegate :clinical_center_code, to: :clinical_center

  after_destroy :nil_primary_cra_id
  after_create :update_sites_vip
  after_create :update_sites_priority_payments

  scope :sp_admin, -> { where(sp_admin: true) }

  def project_role_name
    project_role.try(:project_role)
  end

  def nil_primary_cra_id
    ClinicalCenter.where(primary_cra_id: self.researcher.id).update_all(primary_cra_id: nil)
  end

  def update_sites_vip
    if clinical_center.vip
      ClinicalCenter.joins(:clinical_center_roles)
        .merge(project_role.clinical_center_roles)
        .where.not(id: clinical_center_id, vip: true)
        .each do |site|
          site.update vip: true
        end
    end
  end

  def update_sites_priority_payments
    if clinical_center.priority_payments?
      ClinicalCenter.joins(:clinical_center_roles)
        .merge(project_role.clinical_center_roles)
        .where.not(id: clinical_center_id)
        .where(priority_payments_to: nil)
        .each do |site|
          site.update priority_payments_to: clinical_center.priority_payments_to
        end
    end
  end
end
