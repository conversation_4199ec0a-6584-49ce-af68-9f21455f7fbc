# == Schema Information
#
# Table name: survey_questions
#
#  id         :bigint           not null, primary key
#  question   :text
#  type       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  survey_id  :bigint
#
# Indexes
#
#  index_survey_questions_on_id_and_type  (id,type)
#  index_survey_questions_on_survey_id    (survey_id)
#
# Foreign Keys
#
#  fk_rails_...  (survey_id => surveys.id)
#
class Survey::Question < ApplicationRecord
  belongs_to :survey

  def requires_answer?
    true
  end
end
