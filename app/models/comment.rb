# == Schema Information
#
# Table name: comments
#
#  id            :integer          not null, primary key
#  added_by_type :string(255)
#  body          :text
#  resource_type :string(255)
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  added_by_id   :integer
#  resource_id   :integer
#
# Indexes
#
#  index_comment_on_added_by_id_type  (added_by_id,added_by_type)
#  index_comment_on_resource_id_type  (resource_id,resource_type)
#
class Comment < ApplicationRecord
  belongs_to :added_by, polymorphic: true
  belongs_to :resource, polymorphic: true

  validates :body, :added_by_id, :added_by_type, :resource_id, :resource_type, presence: true
end
