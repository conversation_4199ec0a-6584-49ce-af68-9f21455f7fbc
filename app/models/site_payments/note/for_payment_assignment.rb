# == Schema Information
#
# Table name: site_payments_notes
#
#  id                 :bigint           not null, primary key
#  amount             :decimal(, )
#  approved_at        :datetime
#  currency           :string
#  number             :string
#  state              :string           default("unpaid")
#  title              :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  approved_by_id     :bigint
#  clinical_center_id :bigint
#  project_id         :bigint
#  researcher_id      :bigint
#
# Indexes
#
#  index_site_payments_notes_on_approved_by_id      (approved_by_id)
#  index_site_payments_notes_on_clinical_center_id  (clinical_center_id)
#  index_site_payments_notes_on_number              (number) UNIQUE
#  index_site_payments_notes_on_project_id          (project_id)
#  index_site_payments_notes_on_researcher_id       (researcher_id)
#
# Foreign Keys
#
#  fk_rails_...  (approved_by_id => researchers.id)
#  fk_rails_...  (clinical_center_id => clinical_centers.id)
#  fk_rails_...  (project_id => projects.id)
#  fk_rails_...  (researcher_id => researchers.id)
#
class SitePayments::Note::ForPaymentAssignment < SitePayments::Note
  validate :at_least_one_payment_required
  validate :total_payments_not_higher_than_note_amount

  private

  def at_least_one_payment_required
    if site_payments_payments.empty?
      errors.add(:base, 'At least one payment required.')
    end
  end

  def total_payments_not_higher_than_note_amount
    if site_payments_payments.map(&:amount).sum > amount
      errors.add(:base, 'Total payments amount cannot be higher than note amount.')
    end
  end
end
