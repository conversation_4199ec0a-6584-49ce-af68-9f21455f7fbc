# == Schema Information
#
# Table name: correspondences
#
#  id                                :integer          not null, primary key
#  comments                          :text
#  delivery_type                     :string(255)
#  received_date                     :date
#  state                             :string(255)
#  created_at                        :datetime         not null
#  updated_at                        :datetime         not null
#  clinical_center_id                :integer
#  contract_research_organization_id :integer
#  project_id                        :integer
#
# Indexes
#
#  index_correspondences_on_clinical_center_id                 (clinical_center_id)
#  index_correspondences_on_contract_research_organization_id  (contract_research_organization_id)
#  index_correspondences_on_project_id                         (project_id)
#
class Correspondence < ApplicationRecord
  #attr_accessible :comments, :delivery_type, :received_date, :state, :contract_research_organization_id,
  # :project_id, :clinical_center_id

  belongs_to :contract_research_organization
  belongs_to :project
  belongs_to :clinical_center

  STATES = [
    NEW = 'nowy',
    PROCESSED = 'przetworzony'
  ]

  DELIVERY_TYPES = [
    COURIER = 'kurier',
    LETTER = 'list',
    REGISTERED_LETTER = 'list polecony',
    EMAIL = 'email',
    FAX = 'fax'
  ]

  validates :comments, :delivery_type, :received_date, :state, presence: true

  scope :only_new, -> { where(state: NEW) }
  scope :processed, -> { where(state: PROCESSED) }

  def state_after_toggle
    state == Correspondence::NEW ? Correspondence::PROCESSED : Correspondence::NEW
  end
end
