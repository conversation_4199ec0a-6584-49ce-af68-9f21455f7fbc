# == Schema Information
#
# Table name: internal_transfers
#
#  id                              :bigint           not null, primary key
#  amount                          :decimal(, )
#  cancelled_at                    :datetime
#  currency_type                   :string           default("source")
#  destination_amount              :decimal(, )
#  destination_balance_after       :decimal(, )
#  destination_currency            :string
#  for_resource_type               :string
#  source_amount                   :decimal(, )
#  source_balance_after            :decimal(, )
#  source_currency                 :string
#  title                           :string
#  type                            :string
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  clinical_transfer_id            :bigint
#  currency_exchange_id            :bigint
#  destination_currency_account_id :bigint
#  for_resource_id                 :bigint
#  site_payments_note_id           :bigint
#  source_currency_account_id      :bigint
#
# Indexes
#
#  index_internal_transfers_on_clinical_transfer_id             (clinical_transfer_id)
#  index_internal_transfers_on_currency_exchange_id             (currency_exchange_id)
#  index_internal_transfers_on_destination_currency_account_id  (destination_currency_account_id)
#  index_internal_transfers_on_for_resource_type_and_id         (for_resource_type,for_resource_id)
#  index_internal_transfers_on_id_and_type                      (id,type)
#  index_internal_transfers_on_site_payments_note_id            (site_payments_note_id)
#  index_internal_transfers_on_source_currency_account_id       (source_currency_account_id)
#
# Foreign Keys
#
#  fk_rails_...  (clinical_transfer_id => clinical_transfers.id)
#  fk_rails_...  (destination_currency_account_id => currency_accounts.id)
#  fk_rails_...  (site_payments_note_id => site_payments_notes.id)
#  fk_rails_...  (source_currency_account_id => currency_accounts.id)
#
class InternalTransfer::ToProject < InternalTransfer
  extend Enumerize

  CURRENCY_TYPES = {
    source: 'W walucie konta źródłowego',
    destination: 'W walucie konta docelowego'
  }

  after_create :update_project_saldo

  enumerize :currency_type, in: [:source, :destination]

  validates :currency_type, presence: true, on: :create

  def project
    destination.resource
  end

  def update_project_saldo
    raise 'destination is not project account!' if project.nil?

    project.saldo += destination_amount
    project.save! validate: false
  end
end
