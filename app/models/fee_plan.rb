class FeePlan < ApplicationRecord
  validates :internal_name, :title, :tooltip_text, :title_short, :message_priority, :notes_priority, presence: true

  has_many :fee_plan_rule_enablements
  has_many :fee_plan_rules, through: :fee_plan_rule_enablements

  def rule_active?(rule)
    fee_plan_rules.include?(rule)
  end

  def formatted_processing_message(project)
    return unless processing_message
    
    processing_message.gsub('{{clinical_protocol_code}}', project.clinical_protocol_code)
  end

  def to_s
    title
  end
end
