# == Schema Information
#
# Table name: currency_exchanges
#
#  id                 :bigint           not null, primary key
#  amount             :decimal(30, 2)
#  base_currency      :string
#  currency_rate_used :decimal(30, 4)
#  real_amount        :decimal(, )
#  real_exchange_date :date
#  real_total         :decimal(, )
#  record_type        :string
#  success            :boolean
#  target_currency    :string
#  total              :decimal(30, 2)
#  used_spread_rate   :decimal(, )
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  currency_rate_id   :bigint
#  currency_spread_id :bigint
#  record_id          :bigint
#
# Indexes
#
#  index_currency_exchanges_on_currency_rate_id           (currency_rate_id)
#  index_currency_exchanges_on_currency_spread_id         (currency_spread_id)
#  index_currency_exchanges_on_record_type_and_record_id  (record_type,record_id)
#
# Foreign Keys
#
#  fk_rails_...  (currency_rate_id => currency_rates.id)
#  fk_rails_...  (currency_spread_id => currency_spreads.id)
#
class CurrencyExchange < ApplicationRecord
  validates :amount, :base_currency, :currency_rate_id, :record_id, :record_type, :target_currency, :total, presence: true

  belongs_to :record, polymorphic: true
  belongs_to :currency_rate
  belongs_to :currency_spread

  delegate :rate, to: :currency_rate
  delegate :rate, to: :currency_spread, prefix: :spread, allow_nil: true

  scope :for_internal_transfer, -> { where('record_type like ?', "InternalTransfer%") }
  scope :for_notes, -> { where(record_type: 'ProjectDebitSummary') }
  scope :unprocessed, -> { where(success: [nil, false]) }
  scope :for_note_unpaid, -> { for_notes.where(record: ProjectDebitSummary.not_paid) }
  scope :internal_transfer_unprocessed, -> { for_internal_transfer.unprocessed }

  def non_pln_amount
    if target_currency == 'PLN'
      amount
    else
      total
    end
  end

  def non_pln_currency
    if target_currency == 'PLN'
      base_currency
    else
      target_currency
    end
  end

  def currency_rate_used_with_spread_applied
    currency_rate_used * used_spread_rate
  end
end
