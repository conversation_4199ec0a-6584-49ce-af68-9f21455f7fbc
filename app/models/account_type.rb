class AccountType < ApplicationRecord
  has_many :account_type_settings

  validates :name, presence: true, uniqueness: true
  validate :only_one_default_account

  default_scope { order(position: :asc) }

  scope :for_connected_account, -> { where.not(default_account: true) }

  def self.default
    find_by!(default_account: true)
  end

  private

  def only_one_default_account
    if default_account && AccountType.where(default_account: true).where.not(id: id).exists?
      errors.add(:default_account, 'can only be set for one account type')
    end
  end
end
