# == Schema Information
#
# Table name: action_items
#
#  id                   :bigint           not null, primary key
#  added_by_type        :string
#  deadline             :date
#  description          :text
#  for_site_payments    :boolean          default(FALSE)
#  locale               :string           default("en")
#  number               :string
#  owner                :string
#  resolve_comment      :text
#  resolved_at          :datetime
#  resource_type        :string
#  status               :string
#  type                 :string
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  added_by_id          :bigint
#  resolved_by_id       :bigint
#  resource_id          :bigint
#  target_researcher_id :bigint
#
# Indexes
#
#  index_action_items_on_added_by_type_and_added_by_id  (added_by_type,added_by_id)
#  index_action_items_on_id_and_type                    (id,type)
#  index_action_items_on_resolved_by_id                 (resolved_by_id)
#  index_action_items_on_resource_type_and_resource_id  (resource_type,resource_id)
#  index_action_items_on_target_researcher_id           (target_researcher_id)
#
# Foreign Keys
#
#  fk_rails_...  (resolved_by_id => researchers.id)
#  fk_rails_...  (target_researcher_id => researchers.id)
#
class ActionItem < ApplicationRecord
  include ActionItem::Eventable
  extend Enumerize

  has_many_attached :files

  NUMBER_PREFIX = 'AI'
  RESOURCE_TYPES = {
    'ClinicalCenter' => 'Site',
    'ClinicalUser' => 'Patient'
  }

  attribute :project_id, :integer
  attribute :cro_id, :integer

  belongs_to :resource, polymorphic: true
  belongs_to :added_by, polymorphic: true
  belongs_to :target_researcher, class_name: 'Researcher'
  has_many :escalation_researchers, class_name: 'ActionItem::EscalationResearcher'
  has_many :escalatees, class_name: 'Researcher', through: :escalation_researchers, source: :researcher
  has_many :responses, class_name: 'ActionItem::Message', as: :response_to
  has_many :messages, class_name: 'ActionItem::Message'
  has_many :events, class_name: 'ActionItem::Event', as: :resource
  belongs_to :resolved_by, class_name: 'Researcher'
  has_many :reviews, as: :resource
  has_many :ai_reviews, as: :resource
  has_many :notifications, class_name: 'ActionItem::Notification'
  has_one :project, through: :resource

  enumerize :status, in: %i[new pending unverified_resolved_by_researcher resolved_by_operator], default: :new
  enumerize :owner, in: ['Investigator', 'CRA', 'Manager', 'Operator']
  enumerize :locale, in: %i[en pl]

  accepts_nested_attributes_for :escalation_researchers, reject_if: proc { |attrs| attrs['researcher_id'].blank? }

  validates :description, :deadline, :resource, :added_by, presence: true

  after_create :set_number, unless: proc { |ai| ai.number.present? }

  delegate :project,
    :cro,
    to: :resource, allow_nil: true

  delegate :clinical_protocol_code,
    to: :project, allow_nil: true

  delegate :name,
    to: :cro, prefix: :cro, allow_nil: true

  scope :not_resolved, -> { where(status: ['new', 'pending']) }
  scope :pending, -> { not_resolved.where('action_items.created_at > ?', 7.days.ago) }
  scope :finishing_pending, -> { not_resolved.where('action_items.created_at > ? AND action_items.created_at <= ?', 7.days.ago, 5.days.ago) }
  scope :lvl_1_escalation, -> { not_resolved.where('action_items.created_at > ? AND action_items.created_at <= ?', 14.days.ago, 7.days.ago) }
  scope :finishing_lvl_1_escalation, -> { not_resolved.where('action_items.created_at > ? AND action_items.created_at <= ?', 14.days.ago, 11.days.ago) }
  scope :lvl_2_escalation, -> { not_resolved.where('action_items.created_at <= ?', 14.days.ago) }
  scope :finishing_lvl_2_escalation, -> { not_resolved.where('action_items.created_at <= ?', 17.days.ago) }
  scope :completed, -> { where.not(status: ['new', 'pending']) }

  def not_resolved?
    ['new', 'pending'].include?(status)
  end

  def pending?
    not_resolved? && created_at > 7.days.ago
  end

  def finishing_pending?
    not_resolved? && created_at > 7.days.ago && created_at <= 5.days.ago
  end

  def lvl_1_escalation?
    not_resolved? && created_at > 14.days.ago && created_at <= 7.days.ago
  end

  def finishing_lvl_1_escalation?
    not_resolved? && created_at > 14.days.ago && created_at <= 11.days.ago
  end

  def lvl_2_escalation?
    not_resolved? && created_at <= 14.days.ago
  end

  def finishing_lvl_2_escalation?
    not_resolved? && created_at <= 17.days.ago
  end

  def completed?
    !not_resolved?
  end

  def last_notification
    notifications.order_by_id.first
  end

  def self.for_source(source)
    where(resource: [source.clinical_centers, source.clinical_users, source.visits])
  end

  def available_owners
    index = ActionItem.owner.values.index(default_ai_owner)
    ActionItem.owner.values[index..-1]
  end

  def default_ai_owner
    project&.default_ai_owner || 'Investigator'
  end

  def owner_researcher
    ActionItems::GetOwnerResearcher.call(action_item: self)
  end

  def owner_cc
    "ActionItems::Targets::#{ owner.pluralize.capitalize }::GetCc".constantize.call(action_item: self)
  end

  def first_escalatee
    ActionItems::Escalations::First.call(action_item: self)
  end

  def first_escalation_cc
    ActionItems::Escalations::First::GetCc.call(action_item: self)
  end

  def second_escalatee
    ActionItems::Escalations::Second.call(action_item: self)
  end

  def second_escalation_cc
    ActionItems::Escalations::Second::GetCc.call(action_item: self)
  end

  def clinical_center
    resource.is_a?(ClinicalCenter) ? resource : resource.clinical_center
  end

  def clinical_user
    resource.is_a?(ClinicalUser) ? resource : nil
  end

  def self.for_researcher(researcher)
    if researcher.payclinical_employee
      ActionItem.all
    else
      ActionItem
        .where(added_by: researcher)
        .or(
          ActionItem.where(target_researcher: researcher)
        )
    end
  end

  def set_number
    self.number = ActionItems::GenerateNumber.call(action_item: self)
    self.save! validate: false
  end

  def resolve(resolved_by:, comment: nil)
    update(
      status: resolved_by&.payclinical_employee ? 'resolved_by_operator' : 'unverified_resolved_by_researcher',
      resolved_by: resolved_by,
      resolved_at: Time.current,
      resolve_comment: comment
    )
  end

  def context_name
    if resource.is_a?(ClinicalUser)
      'Subject'
    elsif resource.is_a?(ClinicalCenter)
      'Site'
    else
      resource_type
    end
  end

  def resource_name
    resource ? resource : ''
  end
end
