class BulkInvoice < Invoice
  validates :number, presence: true

  delegate :researcher_for_invoice_email,
           :invoice_email_cc,
           :fee_currency,
           :invoice_currency,
           to: :contract_research_organization, allow_nil: true

  scope :is_valid, -> { where(is_valid: true) }

  def in_foreign_currency?
    invoice_currency != 'PLN'
  end

  def total_in_pln
    return gross_total_amount unless in_foreign_currency?

    Currencies::Convert.call(
      base_currency: invoice_currency,
      target_currency: 'PLN',
      date: created_for_to - 1.day,
      amount: gross_total_amount
    ).round(2)
  end

  def cro
    contract_research_organization
  end

  def get_comment_for_pdf
    return self.comment if self.comment.present?
    return self.contract_research_organization.po if self.contract_research_organization.try(:po).present?
  end

  def projects
    report.projects_with_fees
  end

  def clinical_protocol_codes
    projects.pluck(:clinical_protocol_code).compact.uniq
  end

  def clinical_protocol_code
    clinical_protocol_codes.to_sentence
  end

  def report
    @report ||= CroFeeReport.new(
      cro: contract_research_organization,
      start_date: created_for_from,
      end_date: created_for_to
    )
  end

  def regeneration_form
    GenerateBulkInvoice.new(
      cro: contract_research_organization,
      start_date: created_for_from,
      end_date: created_for_to,
      order_number: order_number_without_letters
    )
  end

  def report_for_project(project)
    ProjectFeeReport.new(project_id: project.id, start_date: created_for_from.beginning_of_day, end_date: created_for_to.end_of_day)
  end
end
