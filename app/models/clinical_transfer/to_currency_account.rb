# == Schema Information
#
# Table name: clinical_transfers
#
#  id                                       :integer          not null, primary key
#  account_number                           :string(255)
#  amount                                   :decimal(, )
#  amount_left                              :decimal(, )
#  auto_accepted_within_amount_and_km_range :boolean
#  balance                                  :decimal(, )
#  comment                                  :text
#  confirmation_sent_at                     :datetime
#  currency_account_balance_after           :decimal(, )
#  destination_record_type                  :string(255)
#  emergency                                :boolean          default(FALSE)
#  flow_direction                           :string(1)
#  is_refund                                :boolean          default(FALSE)
#  name                                     :string(255)
#  priority                                 :boolean          default(FALSE)
#  project_saldo                            :decimal(, )
#  reconciled                               :boolean          default(FALSE)
#  reference_number                         :string(255)
#  reject_reason                            :text
#  rejected_at                              :datetime
#  saldo_after_status_changed               :decimal(, )
#  source_acc_nr                            :string
#  source_name                              :string
#  source_record_type                       :string(255)
#  state                                    :string(1)
#  status_change_date                       :datetime
#  title                                    :string(255)
#  transfer_accepted_at                     :datetime
#  transfer_type                            :string(255)
#  transfered_to                            :string(255)      default("clinical_user")
#  type                                     :string(255)
#  was_internal                             :boolean          default(FALSE)
#  created_at                               :datetime         not null
#  updated_at                               :datetime         not null
#  alior_file_id                            :integer
#  alior_transaction_id                     :string
#  citi_file_id                             :integer
#  clinical_center_id                       :integer
#  clinical_user_id                         :integer
#  currency_account_id                      :bigint
#  destination_record_id                    :integer
#  in_citi_file_id                          :integer
#  master_transfer_id                       :integer
#  project_debit_id                         :integer
#  project_debit_summary_id                 :integer
#  project_id                               :integer
#  reference_clinical_transfer_id           :string(255)
#  rejected_by_researcher_id                :integer
#  researcher_id                            :integer
#  simp_file_id                             :integer
#  site_payments_payment_id                 :bigint
#  source_record_id                         :integer
#  transfer_accepted_by_researcher_id       :integer
#  visit_id                                 :integer
#
# Indexes
#
#  index_clinical_transfers_on_alior_file_id                       (alior_file_id)
#  index_clinical_transfers_on_alior_transaction_id                (alior_transaction_id)
#  index_clinical_transfers_on_citi_file_id                        (citi_file_id)
#  index_clinical_transfers_on_clinical_center_id                  (clinical_center_id)
#  index_clinical_transfers_on_clinical_user_id                    (clinical_user_id)
#  index_clinical_transfers_on_currency_account_id                 (currency_account_id)
#  index_clinical_transfers_on_destination_record                  (destination_record_id,destination_record_type)
#  index_clinical_transfers_on_id_and_type                         (id,type)
#  index_clinical_transfers_on_in_citi_file_id                     (in_citi_file_id)
#  index_clinical_transfers_on_master_transfer_id                  (master_transfer_id)
#  index_clinical_transfers_on_project_debit_id                    (project_debit_id)
#  index_clinical_transfers_on_project_debit_summary_id            (project_debit_summary_id)
#  index_clinical_transfers_on_project_id                          (project_id)
#  index_clinical_transfers_on_reference_clinical_transfer_id      (reference_clinical_transfer_id)
#  index_clinical_transfers_on_rejected_by_researcher_id           (rejected_by_researcher_id)
#  index_clinical_transfers_on_researcher_id                       (researcher_id)
#  index_clinical_transfers_on_simp_file_id                        (simp_file_id)
#  index_clinical_transfers_on_site_payments_payment_id            (site_payments_payment_id)
#  index_clinical_transfers_on_source_record                       (source_record_id,source_record_type)
#  index_clinical_transfers_on_state                               (state)
#  index_clinical_transfers_on_transfer_accepted_by_researcher_id  (transfer_accepted_by_researcher_id)
#  index_clinical_transfers_on_visit_id                            (visit_id)
#
# Foreign Keys
#
#  clinical_transfers_citi_file_id_fk                        (citi_file_id => citi_files.id)
#  clinical_transfers_clinical_user_id_fk                    (clinical_user_id => clinical_users.id)
#  clinical_transfers_in_citi_file_id_fk                     (in_citi_file_id => citi_files.id)
#  clinical_transfers_project_debit_id_fk                    (project_debit_id => project_debits.id)
#  clinical_transfers_project_debit_summary_id_fk            (project_debit_summary_id => project_debit_summaries.id)
#  clinical_transfers_project_id_fk                          (project_id => projects.id)
#  clinical_transfers_researcher_id_fk                       (researcher_id => researchers.id)
#  clinical_transfers_simp_file_id_fk                        (simp_file_id => simp_files.id)
#  clinical_transfers_transfer_accepted_by_researcher_id_fk  (transfer_accepted_by_researcher_id => researchers.id)
#  clinical_transfers_visit_id_fk                            (visit_id => visits.id)
#  fk_rails_...                                              (currency_account_id => currency_accounts.id)
#  fk_rails_...                                              (site_payments_payment_id => site_payments_payments.id)
#
class ClinicalTransfer::ToCurrencyAccount < ClinicalTransfer
  before_validation :set_default_attrs

  validates :source_acc_nr, :currency_account_id, presence: true

  after_create_commit :update_currency_account

  def set_default_attrs
    self.status_change_date = Time.new
    self.flow_direction = ClinicalTransfer::INCOMING
    self.state = ClinicalTransfer::BOOKED
  end

  def update_currency_account
    currency_account.balance += amount
    currency_account.save! validate: false
  end
end
