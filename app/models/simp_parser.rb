# -*- encoding : utf-8 -*-
class SimpException < Exception
  def initialize(log_file, line)
    puts line
    log = File.open(log_file,'a')
    log.puts(line)
    log.close
  end
end

class TransactionException < SimpException;end
class InvalidAccountException < SimpException;end
class TooBigSumException < SimpException;end
class IngHeaderInvalidException < SimpException;  end
class WrongCurrencyCodeException < SimpException; end
class AmountLessThanZeroException < SimpException; end
class NoSuchProjectDebitException < SimpException; end



# 79 1030 0019 0109 8533 0002 4745
class SimpParser

  def initialize(simp_file_path)
    # plik z simp xml
    @warning = false # flaga czy czasem jakis przelew sie nie wysypal
    @doc = open_file(simp_file_path)
    @log_file = simp_file_path + '_log_' + Time.now.strftime("%m-%d-%Y_%H_%M_%S")+".txt"
    # reference number prefix
    #@reference_num_prefix = file_path.gsub(/.*LetMePay_/,'').gsub(/\.txt/,'')
  end

  def add_funds(simp_file_id)

    validate_ing_xml_header!(@doc.xpath('//NAGLOWEK'))
    @reference_num_prefix = @doc.xpath('//DATA_DO').text.gsub(/-/,'')

    log("SIMP XML File reference number: #{@reference_num_prefix}, upload started at: #{Time.new}")
    log("-"*80)

    # iteracja po LICZBA_POZYCJACH # bedzie bezpieczniej?
      # wyciągniecie interesujących nas danych do zmiennych
      # (kwota, kierunek_przelewu, nr_rachunku_docelowego_usera, tytul, nazwa, zrodlowy_rachunek)
      # tylko pozycje mówiące o przelewie przychodzącym => inne nie brance pod uwagę
    @doc.xpath('//POZYCJA').each do |p|
      begin
        position = {
          'lp'                => p.xpath('LP').text,
          'kwota_uznanie'     => p.xpath('UZNANIE').text,
          'nr_klienta'        => p.xpath('NR_KLIENTA').text,
          'd_operacji'        => p.xpath('DATA_OPERACJI').text,
          'd_ksiegowania'     => p.xpath('DATA_KSIEGOWANIA').text,
          'tytul'             => p.xpath('TYTUL').text,
          'klient_nazwa'      => p.xpath('KLIENT_NAZWA').text,
          'klient_adres'      => p.xpath('KLIENT_ADRES').text,
          'klient_rachunek'   => p.xpath('KLIENT_RACHUNEK').text,
          'reference_number'  => @reference_num_prefix +"LP" + p.xpath('LP').text
        }
        log(position)


        if position['nr_klienta'] == "79103000190109853300024745"
          # sumaryczna nota obciążeniowa dla quintiles
          project_debit_summary = find_project_debit_summary!(position['tytul'])
          #validate_project_debit_summary_position!(position, project_debit_summaries)
          book_project_debit_summary(position, project_debit_summary, simp_file_id)

        else # klasycznie debet zasilany
          project_debit = find_project_debit!(position['nr_klienta'])
          validate_current_position!(position, project_debit)
          book_transaction!(position, project_debit, simp_file_id)
        end

      # kwota mniejsza od 0, transakcja juz dodana do bazy -> nic nie robie
      rescue TransactionException, AmountLessThanZeroException => e
        puts "ERROR: TransactionException || AmountLessThanZeroException"
        # ja tylko uzywam gdy juz taka transakcja zostala dodana zatem nic nie robie,
        # warto zmienic nazwe na TransactionAlreadyBooked?
        # nic nie robie

      rescue InvalidAccountException, IngHeaderInvalidException => e
        @warning = true
        log("### FATAL, upload XML przerwany")
        return
        # Nieprawidłowy numer konta, nieprawidłowy nagłowek -> Fatal koncze wgrywanie

      rescue Exception => e
        @warning = true
        puts $!, $@
        log("Błąd, ostatni z przelewow z pliku nie został dodany.")
        p e
        raise e
        #raise "Błąd. Przetwarzany przelew nie został dodany."

      rescue NoSuchProjectDebitException
        @warning = true
        puts "ERROR: NoSuchProjectDebitException"

      ensure
        log("-"*80)
        next
      end

    end
    # TODO zwracam ostatni project_debit
    return [@warning, @log_file]
  end


  def find_project_debit_summary!(note_number)
    project_debit_summary = ProjectDebitSummary.sent.where(note_number: note_number).first

    unless project_debit_summary
      raise NoSuchProjectDebitException.new(@log_file,
        "BLAD: Nie znaleziono noty obciążeniowej(ProjectDebitSummary) dla Quintiles o numerze: #{note_number}")
    end
    project_debit_summary
  end


  def book_project_debit_summary(position, pds, simp_file_id)
    ActiveRecord::Base.transaction(requires_new: true) do

      if pds.project_debits.size > 0
        # ksieguje przychodzacy przelew podpinam do project_debit_summary...
        clinical_transfer_params = {
          project_debit_summary_id: pds.id,
          simp_file_id:     simp_file_id,
          amount:           position['kwota_uznanie'],
          flow_direction:   ClinicalTransfer::INCOMING,
          state:            ClinicalTransfer::BOOKED,
          status_change_date: Time.new,
          name:             position['klient_nazwa'],
          title:            position['tytul'],
          account_number:   position['klient_rachunek'],
          reference_number: position['reference_number'],
        }
        ct = ClinicalTransfer.new(clinical_transfer_params)
        check_transaction!(ct)

        pds.saldo += ct.amount
        if ct.amount >= pds.saldo.abs
          pds.state = ProjectDebitSummary::PAID
          pds.state_changed_at = Time.new
        end
        pds.save!
      end

      money_left = ct.amount
      pds.project_debits.order('saldo desc').each do |pd|

        if pd.saldo.abs <= money_left
          allowed_amount = pd.saldo.abs
          money_left -= pd.saldo.abs
        elsif pd.saldo.abs > money_left
          allowed_amount = money_left
          money_left = 0
        end


        book_project_debit_transaction!(ct, allowed_amount, pd, simp_file_id)
      end

    end

  end


  def book_project_debit_transaction!(clinical_transfer, amount_to_spend, project_debit, simp_file_id)
    ActiveRecord::Base.transaction(requires_new: true) do
      ct = clinical_transfer
      # zwiekszenie salda project_debit oraz projektu
      project = project_debit.project
      log("Przed zasileniem: saldo projektu: #{project.saldo}, saldo zestawienia: #{project_debit.saldo}")

      # status na PAID tylko gdy saldo wyzerowane lub przekroczone
      #_amount = project_debit.saldo ct.amount
      project_debit.saldo = project_debit.saldo + amount_to_spend
      if project_debit.saldo == 0
        project_debit.status = ProjectDebit::PAID
        project_debit.status_change_date = Time.new
        project_debit.save!
        log("Zmieniono status zestawienia na opłacone.")
      elsif project_debit.saldo > 0
        project_debit.status = ProjectDebit::PAID
        project_debit.status_change_date = Time.new
        project_debit.save!
        @warning = true
        log("UWAGA! Zmieniono status zestawienia na opłacone (przelano kwotę wiekszą niż wymagana).")
      elsif project_debit.saldo < 0
        project_debit.status_change_date = Time.new
        project_debit.save!
        @warning = true
        log("UWAGA! Zestawienie opłacone częściowo (przelano kwotę mniejszą niż wymagana).")
      end

      project.saldo = project.saldo + ct.amount
      project.save!
      ct.project_saldo = project.saldo
      ct.save!

      log("Po zasileniu: saldo projektu: #{project.saldo}, saldo zestawienia: #{project_debit.saldo}")

      # TODO czy kwota przelewu jest taka sama jak suma kwot za zlecone do tej pory przelewy ?

      # NOTYFIKACJA SMS, jesli projekt byl rozliczany debetowo, to w ramach tego salda
      # oraz ustalanie ktore przelewy wyjdą
      if project.debit_allowed

        # Maksymalna kwota jaka mozemy wydac aprobująć przelewy:
        # Kwota dostepna na zlecenie(zmiane statusu na PROCESSING) przelewow =
        # = Łączna suma zasilen we wszystkich projektach i zestawieniach danej sumarycznej noty obciążeniowej -
        #  Suma juz wydanych(status=ClinicalTransfer::PROCESSING) pieniedzy w ramach tej noty i jej zestawieniach
        incoming_sum = ClinicalTransfer.unscoped.where(['project_debit_id = ? OR project_debit_summary_id = ?',
                                                       project_debit.id, project_debit.project_debit_summary_id]).
                                                 where(flow_direction: ClinicalTransfer::INCOMING).
                                        inject(0) { |sum, i| sum + i.amount }

        outgoing_sum = ClinicalTransfer.unscoped.where(project_debit_id: project_debit.project_debit_summary.
                                        project_debits.map(&:id)).where(flow_direction: ClinicalTransfer::OUTGOING).
                                        where(state: ClinicalTransfer::PROCESSING).
                                        inject(0) { |sum, i| sum + i.amount }

        allowed_amount = incoming_sum - outgoing_sum

        #puts "#{allowed_amount} = #{incoming_sum} - #{outgoing_sum}"

        if allowed_amount > 0
          ClinicalTransfer.unscoped.where(project_debit_id: project_debit.id)
                                   .where(flow_direction: ClinicalTransfer::OUTGOING)
                                   .where(state: ClinicalTransfer::DEBIT_WAITING).
                                   order('amount ASC').each do |pd_ct|

            next if (allowed_amount - pd_ct.amount) < 0
            pd_ct.update!(state: ClinicalTransfer::PROCESSING, simp_file_id: simp_file_id )
            sms = pd_ct.clinical_user.clinical_mobile_messages.build
            sms.send_bank_transfer_confirmation(pd_ct)
            allowed_amount -= pd_ct.amount
          end
        end
      end

      # TODO info na email?
      log("Transakcja zaksięgowana: clinical_transfer_id: #{ct.id}, project_debit_id: #{project_debit.id} ")
    end

  end


  # :clinical_user_id => :integer,
  # :researcher_id => :integer,
  # :amount => :decimal,
  # :flow_direction => :string,
  # :state => :string,
  # :status_change_date => :datetime,
  # :reference_number => :string,
  # :account_number => :string,
  # :name => :string,
  # :title => :string,
  def book_transaction!(position, project_debit, simp_file_id)

    ActiveRecord::Base.transaction(requires_new: true) do

      clinical_transfer_params = {
        project_id:       project_debit.project_id,
        project_debit_id: project_debit.id,
        simp_file_id:     simp_file_id,
        amount:           position['kwota_uznanie'],
        flow_direction:   ClinicalTransfer::INCOMING,
        state:            ClinicalTransfer::BOOKED,
        status_change_date: Time.new,
        name:             position['klient_nazwa'],
        title:            position['tytul'],
        account_number:   position['klient_rachunek'],
        reference_number: position['reference_number'],
      }

      ct = ClinicalTransfer.new(clinical_transfer_params)
      check_transaction!(ct)

      # zwiekszenie salda project_debit oraz projektu
      project = project_debit.project
      log("Przed zasileniem: saldo projektu: #{project.saldo}, saldo zestawienia: #{project_debit.saldo}")

      # status na PAID tylko gdy saldo wyzerowane lub przekroczone
      project_debit.saldo = project_debit.saldo + ct.amount
      if project_debit.saldo == 0
        project_debit.status = ProjectDebit::PAID
        project_debit.status_change_date = Time.new
        project_debit.save!
        log("Zmieniono status zestawienia na opłacone.")
      elsif project_debit.saldo > 0
        project_debit.status = ProjectDebit::PAID
        project_debit.status_change_date = Time.new
        project_debit.save!
        MatrixClinicalMailer.with(project_debit: project_debit, simp_file_id: simp_file_id).invalid_simp_amount.deliver
        log("UWAGA! Zmieniono status zestawienia na opłacone (przelano kwotę wiekszą niż wymagana).")
      elsif project_debit.saldo < 0
        project_debit.status_change_date = Time.new
        project_debit.save!
        MatrixClinicalMailer.with(project_debit: project_debit, simp_file_id: simp_file_id).invalid_simp_amount.deliver
        log("UWAGA! Zestawienie opłacone częściowo (przelano kwotę mniejszą niż wymagana).")
      end

      project.saldo = project.saldo + ct.amount
      project.save!
      ct.project_saldo = project.saldo
      ct.save!

      log("Po zasileniu: saldo projektu: #{project.saldo}, saldo zestawienia: #{project_debit.saldo}")

      # TODO czy kwota przelewu jest taka sama jak suma kwot za zlecone do tej pory przelewy ?

      # NOTYFIKACJA SMS, jesli projekt byl rozliczany debetowo, to w ramach tego salda
      # oraz ustalanie ktore przelewy wyjdą
      if project.debit_allowed

        # Kwota dostepna na zlecenie(zmiane statusu na PROCESSING) przelewow =
        # = Suma zasileń - Suma juz wydanych(status=ClinicalTransfer::PROCESSING)
        incoming_sum = project_debit.clinical_transfers.
                                    where(flow_direction: ClinicalTransfer::INCOMING).
                                    inject(0) { |sum, i| sum + i.amount }
        outgoing_sum = project_debit.clinical_transfers.
                                    where(flow_direction: ClinicalTransfer::OUTGOING).
                                    where(state: ClinicalTransfer::PROCESSING).
                                    inject(0) { |sum, i| sum + i.amount }

        allowed_amount = incoming_sum - outgoing_sum

        #puts "#{allowed_amount} = #{incoming_sum} - #{outgoing_sum}"

        if allowed_amount > 0
          ClinicalTransfer.unscoped.where(project_debit_id: project_debit.id)
                                   .where(flow_direction: ClinicalTransfer::OUTGOING)
                                   .where(state: ClinicalTransfer::DEBIT_WAITING).
                                   order('amount ASC').each do |pd_ct|

            next if (allowed_amount - pd_ct.amount) < 0
            pd_ct.update!(state: ClinicalTransfer::PROCESSING, simp_file_id: simp_file_id )
            sms = pd_ct.clinical_user.clinical_mobile_messages.build
            sms.send_bank_transfer_confirmation(pd_ct)
            allowed_amount -= pd_ct.amount
          end
        end
      end

      # TODO info na email?
      log("Transakcja zaksięgowana: clinical_transfer_id: #{ct.id}, project_debit_id: #{project_debit.id} ")
    end
  end

  # sprawdza czy transakcja nie była wczesniej zaksięgowana
  def check_transaction!(ct)
    transaction = ClinicalTransfer.where(["reference_number = ? AND flow_direction = ?",
                                          ct.reference_number, ct.flow_direction]).first
    unless transaction.nil?
      raise TransactionException.new(@log_file,
        "Przetwarzana transakcja jest już obecna w bazie REF_NUM: #{transaction.reference_number} ID: #{transaction.id}")
    end
  end

  def open_file(file_path)
    Nokogiri::XML( File.open( file_path ) )
  end

  # metoda zapisująca do pliku z dziennikiem oraz na stdout
  def log(line)
    puts line
    log = File.open(@log_file,'a')
    log.puts(line)
    log.close
  end

  #sprawdza czy pieniadze zostaly zaksiegowane na wlasciwym koncie
  def check_account_number(account)
    if !account.eql?('43105010121000002361418755') # był: **********
      raise InvalidAccountException.new(@log_file,"Numer konta nieprawidlowy, podany numer #{account}")
    end
  end

  # waliduje poprawność nagłówka
  # czy INGBANK
  # czy NR konta prawidłowy
  # czy data < Time.new przeszła?
  # TODO: czy suma uznań taka sama jak w pozycjach
  #  3 <NAGLOWEK>
  #  4 <WYSTAWCA>INGBANK</WYSTAWCA>
  #  5 <DATA_OD>2012-05-08</DATA_OD>
  #  6 <DATA_DO>2012-05-08</DATA_DO>
  #  7 <NR_RACHUNKU>43105010121000002361418755</NR_RACHUNKU>
  #  8 <NR_WYCIAGU>24</NR_WYCIAGU>
  #  9 <UZNANIA>124.04</UZNANIA>
  # 10 <OBCIAZENIA>0.00</OBCIAZENIA>
  # 11 <LICZBA_POZYCJI>4</LICZBA_POZYCJI>
  # 12 </NAGLOWEK>
  def validate_ing_xml_header!(doc)
    if Time.parse(doc.xpath('DATA_OD').text) > Time.new || Time.parse(doc.xpath('DATA_DO').text) > Time.new
      raise IngHeaderInvalidException.new(@log_file,"Data ksiegowania/przelewu ma miejsce w przyszłości")
    end

    if !doc.xpath('WYSTAWCA').text.eql?("INGBANK")
      raise IngHeaderInvalidException.new(@log_file,"Wartosc w polu WYSTAWCA inna niz INGBANK")
    end
    check_account_number(doc.xpath('NR_RACHUNKU').text) #rzuca InvalidAccountException
  end


  def find_project_debit!(account_nu)
    project_debit = ProjectDebit.where(account_number: account_nu).where('status != ?',ProjectDebit::STARTED).first

    if project_debit.nil?
      raise NoSuchProjectDebitException.new(@log_file,"BLAD: Nie znaleziono zestawienia(ProjectDebit) dla numeru rachunku: #{account_nu}.")
    end
    project_debit
  end


   # 26 <POZYCJA>
   # 27 <LP>2</LP>
   # 28 <UZNANIE>123.02</UZNANIE>
   # 29 <NR_KLIENTA>70105000996800000698474798</NR_KLIENTA>
   # 30 <DATA_OPERACJI>2012-05-07</DATA_OPERACJI>
   # 31 <DATA_KSIEGOWANIA>2012-05-08</DATA_KSIEGOWANIA>
   # 32 <TYTUL>TEST|||</TYTUL>
   # 33 <KLIENT_NAZWA>MI&#321;OSZ  KACZOROWSKI||UL.PU&#321;AWSKA BL.257 M.47|02-769 WARSZAWA</KLIENT_
   # 34 <KLIENT_ADRES></KLIENT_ADRES>
   # 35 <KLIENT_RACHUNEK>58114020040000300249513389</KLIENT_RACHUNEK>
   # 36 </POZYCJA>
  def validate_current_position!(position, project_debit)

    if position['nr_klienta'][14] != "2"
      raise InvalidAccountException.new(@log_file, "Nie zgadza sie prefix zdefiniowany dla clinical")
    end
    project_id = position['nr_klienta'][15..21].to_i
    if ! Project.find(project_id)
      raise InvalidAccountException.new(@log_file, "Nie odnaleziono projektu o id: #{project_id}
       dla ktorego został wygenerowany rachunek #{position['nr_klienta']}")
    end


  end



end
