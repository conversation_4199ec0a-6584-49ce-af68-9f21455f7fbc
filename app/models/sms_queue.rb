class SmsQueue < ApplicationRecord
  belongs_to :clinical_mobile_message

  MULTI_INFO_PROVIDER = 'MULTIINFO'

  after_create :send_message, if: -> { self.pending? }
  include AASM

  aasm column: :status do
    state :pending, initial: true
    state :sending, :waiting, :queued, :failed, :sent

    event :send_sms, after_commit: :call_provider do
      transitions from: [:pending, :waiting, :queued, :failed], to: :sending
    end

    event :queue, after_commit: :set_queued_at  do
      transitions from: [:queued, :pending, :sending], to: :queued
    end

    event :wait, after_commit: :wait_before_send do
      transitions from: [:pending, :queued, :failed], to: :waiting
    end

    event :fail do
      transitions from: [:sending, :pending, :queued, :failed], to: :failed
    end

    event :mark_as_sent, after_commit: :set_sent_at do
      transitions from: [:sending], to: :sent
    end
  end

  def send_message
    sms = self
    if sms.priority
      sms.send_sms!
    elsif Time.now.hour.between?(8, 21)
      sms.send_sms!
    else
      sms.queue!
    end
  end

  def self.send_queued_messages
    SmsQueue.queued.each do |m|
      m.send_message
    end
  end

  def to_s
    return "[#{phone_number}][P: #{priority}][Test: #{testmode}]: #{body}"
  end

  def wait_before_send
    sec_later = [*(1.minute.seconds.to_i)..(5.minute.seconds.to_i)].sample
    SmsQueueCallProviderJob.perform_in(sec_later, self)
  end

  private

  def set_queued_at
    self.queued_at = DateTime.now
    self.save
  end

  def set_sent_at
    self.sent_at = DateTime.now
    self.save
  end

  def set_provider(provider)
    self.update(provider: provider)
  end

  def call_provider(provider: SiteSetting.first_or_create!.default_sms_provider)
    return if self.sent?

    set_provider(provider)
    provider_response = case provider
    when "SMSAPI"
      smsapi_provider
    when "SMS-1"
      raspberry_provider(device_number: 1)
    when "SMS-2"
      raspberry_provider(device_number: 2)
    when MULTI_INFO_PROVIDER
      multi_info_provider
    else
      false
    end

    if provider_response == true
      self.mark_as_sent!
      return true
    else
      self.fail!
      return false
    end

  rescue Exception => e
    self.fail!
    p e
    p e.backtrace
    p "Nie wyslano sms: #{self.errors.messages.to_a}"
    ExceptionNotifier.notify_exception(e, data: { message: "Blad przy wysylaniu sms #{ self.try(:id) }. Record: #{ self.try(:attributes) }" })
    return false
  end

  def raspberry_provider(device_number: 1)
    if Rails.env.development?
      dev_mode_sms_message!("RASPBERRY_PROVIDER #{device_number}")
      return true
    end

    if Rails.env.test?
      self.testmode = true
      return true
    end

    caller = RaspberrySmsService.new(
      phone_number: phone_number,
      msg: body,
      testmode: testmode,
      priority: priority,
      device_number: device_number
    )

    response = caller.call
    return response
  end

  def smsapi_provider
    if Rails.env.development?
      dev_mode_sms_message!('SMSAPI PROVIDER')
      return true
    end

    if Rails.env.test?
      self.testmode = true
      return true
    end

    caller = SmsApi.new(phone_number: phone_number, msg: body, testmode: testmode, priority: priority)
    response = caller.call
    return response
  end

  def multi_info_provider
    if Rails.env.development?
      dev_mode_sms_message!('MULTIINFO PROVIDER')
      return true
    end

    if Rails.env.test?
      self.testmode = true
      return true
    end

    MultiInfoSms.new(phone_number: phone_number, msg: body).call
  end

  private

  def dev_mode_sms_message!(provider)
    self.testmode = true
    puts '=========================='
    puts 'development mode: SMS'
    puts provider
    puts "#{self}"
    puts '=========================='
  end
end
