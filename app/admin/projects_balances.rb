ActiveAdmin.register_page 'Bilans zasilen i wyplat' do
  content do
    params[:use_html] = true
    page = ProjectsBalancePage.new(params: params)

    render 'show', page: page
  end

  page_action :generate_report, method: :get do
    params[:use_html] = false
    page = ProjectsBalancePage.new(params: params)

    if params[:xls].present?
      render xlsx: 'report', filename: 'bilans.xlsx', disposition: 'inline', locals: { page: page }
    else
      redirect_to admin_bilans_zasilen_i_wyplat_path(params)
    end
  end

  page_action :generate_balance_report, method: :get do
    page = ProjectsBalancePage.new(params: params[:page_params])
    projects = if params[:project_id].present?
                 Project.where(id: params[:project_id])
               else
                 page.send(params[:projects_scope])
    end

    transfers = page.incoming_transfers(projects).to_a
    transfers << page.outgoing_transfers(projects).to_a
    transfers = transfers.flatten.sort_by(&:status_change_date)

    render xlsx: 'balance_report', filename: 'bilans.xlsx', disposition: 'inline', locals: { transfers: transfers }
  end
end
