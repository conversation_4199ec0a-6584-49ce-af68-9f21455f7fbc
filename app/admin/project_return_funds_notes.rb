ActiveAdmin.register ProjectReturnFundsNote do
  menu parent: 'corrective_notes'

  filter :project
  filter :amount
  filter :title
  filter :account_number

  show do
    attributes_table do
      row :project
      row :amount
      row :title
      row :account_number
      row :number
      row :created_at
      row :pdf do
        if resource.pdf.present?
          link_to resource.pdf.filename.to_s, rails_blob_path(resource.pdf, disposition: 'inline')
        else
          '-'
        end
      end
    end
  end

  action_item :action, only: :show do
    link_to 'Zregeneruj PDF', [:regenerate_pdf, :admin, resource]
  end

  member_action :regenerate_pdf do
    resource.create_pdf

    redirect_to [:admin, resource], notice: 'PDF został wygenerowany ponownie'
  end
end