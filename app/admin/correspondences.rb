

ActiveAdmin.register Correspondence do
  scope 'Nowe', :only_new
  scope 'Przetworzone', :processed

  index do
    id_column
    column :received_date
    column :delivery_type
    column :state
    column :contract_research_organization
    column :project
    column :clinical_center
    column :comments
    actions
  end

  form partial: 'form'

  action_item :action, only: :show do
    link_to "Oznacz jako #{resource.state_after_toggle}", toggle_state_admin_correspondence_path(resource)
  end

  member_action :toggle_state do
    new_state = resource.state_after_toggle
    if resource.update state: new_state
      redirect_back_with_default notice: "Status został zmieniony na #{new_state}"
    else
      redirect_back_with_default alert: "Błąd: #{resource.errors.full_messages.to_sentence}"
    end
  end
end
