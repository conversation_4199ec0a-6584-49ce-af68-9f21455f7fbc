ActiveAdmin.register HelpdeskEmailCall do
  menu false

  belongs_to :helpdesk_email, optional: true, parent_class: HelpdeskEmail

  form do |f|
    f.inputs 'Task' do
      f.input :helpdesk_email_id, value: params[:helpdesk_email_id], as: :hidden
      f.input :assigned_to_id, collection: Researcher.operators.payclinical_employees.distinct.order('last_name asc, first_name asc'), as: :select, label: 'Przypisz do badacza'
      f.input :assigned_to_type, value: 'Researcher', as: :hidden
      f.input :assigned_by_id, value: current_admin_user.id, as: :hidden
      f.input :assigned_by_type, value: 'AdminUser', as: :hidden
    end
    f.actions
  end
end