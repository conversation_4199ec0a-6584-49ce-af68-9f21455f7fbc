class ClinicalTransferPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      scope
    end
  end

  def change_destination?
    manager? || transfer_creator?
  end

  def show?
    if user.is_a?(Researcher)
      user.has_role_in_project?(project: project)
    else
      record.clinical_user_id == user.id
    end
  end

  def accept?
    return false if user == record.researcher
    if project.transfer_accept_roles == 'manager'
      (user_role == 'Manager') && user_transfer_limit_ok?
    elsif project.transfer_accept_roles == 'cra_manager'
      ['CRA+', 'Manager'].include?(user_role) && user_transfer_limit_ok?
    else
      false
    end
  end

  def reject?
    accept? || record.researcher == user || user_role == 'Operator'
  end

  def cancel?
    manager?
  end

  def access_confirmation?
    user.visits.pluck(:id).include? record.visit_id
  end

  def project
    record.try(:get_project)
  end

  def user_role
    @user_role ||= user.role_in_project(project).try(:project_role)
  end

  def user_transfer_limit
    project.reload.transfer_limit_for_role(user_role)
  end

  def user_transfer_limit_ok?
    user_transfer_limit >= record.amount
  end

  def creators_role
    record.researcher.role_in_project(project).try(:project_role) || ''
  end

  def creators_role_normalized
    creators_role.gsub('+', '_plus').downcase
  end

  private

  def manager?
    user_role == 'Manager'
  end

  def transfer_creator?
    record.researcher_id == user.id
  end
end
