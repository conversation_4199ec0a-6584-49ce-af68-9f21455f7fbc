- projects_scope = current_researcher.open_projects.order('clinical_protocol_code asc')
- last_change_in_roles = current_researcher.project_roles.maximum(:updated_at)

// FYI most projects are touched/updated frequently - keep in mind while working with cache
- cache [current_researcher.id, projects_scope.maximum(:updated_at), last_change_in_roles] do
  - projects = projects_scope.pluck_to_struct(:id, :name, :clinical_protocol_code, :test_project, :updated_at)

  - if projects.blank?
    span style=("margin-top: 15px;display: block;")
      | You are not authorized to access any clinical center
  - else
    - projects.each do |project|
      - cache [:project_cc_link, current_researcher.id, project.id, project.updated_at, last_change_in_roles] do

        = link_to 'javascript:;', data: { behavior: 'toggle_visibility', target: "#project_cc_list_#{ project.id }" } do
          span = project.clinical_protocol_code
          - if project.test_project
            span.blue_color style='font-size: 17px;display: block;margin-top: -10px;text-transform: initial;line-height: 1;margin-top: -5px;'
              | Training account
          - else
            span style='font-size: 17px;display: block;margin-top: -10px;color: initial;text-transform: initial;line-height: 1;color: #7e7e7e;margin-top: -5px;'
              = project.name

        ul.project_cc_list id="project_cc_list_#{ project.id }" style='display: none;'
          - clinical_centers = current_researcher.clinical_centers.where(project_id: project.id).cc_code_order.pluck_to_struct(:id, :clinical_center_code)

          - if clinical_centers.blank?
            = link_to v2_sponsor_project_clinical_centers_path(project.id, scope: 'active'), class: 'waves-effect', style: 'padding-left: 46px;' do
              | There are no sites
          - else
            - clinical_centers.each do |cc|
              = link_to v2_sponsor_clinical_center_clinical_users_path(cc.id), class: 'waves-effect', style: 'padding-left: 46px;' do
                span = "Site #{cc.clinical_center_code}"
