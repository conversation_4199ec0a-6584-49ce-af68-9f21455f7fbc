section#about-us-section.section.section-xl.py-lg.pb-200
  .container.pt-lg
    .invitation-text
      div SUBJECT REIMBURSEMENT
      div IN CLINICAL TRIALS

section.section.section-lg.pb-4.py-lg
  .container
    .row.row-grid.align-items-center
      .col-md-12.order-md-1
        .pr-md-5
          h2.display-4 = t('researcher_account_activation.title')

          = simple_form_for(@researcher, url: update_researcher_invitation_path(id: @researcher.id), defaults: {wrapper: :vertical_md_form}, method: :get) do |f|


            .mt-4
            = f.full_error :reset_password_token

            .form-group.mt-2.mb-4
              label Title
              = f.input_field :title, collection: Researcher::TITLES, style: 'width: 100%;', include_blank: false


            = f.input :first_name
            = f.input :last_name
            = f.input :email
            = f.input :phone_number, value: @researcher.phone_number

            .summary_section.default
              | In case of a lost or forgotten password, you will have the option to receive a text message (SMS) with temporary password to recover your account.

            #password_requirements.mt-5
              = render 'shared/researcher_password_requirements'

            = f.input :password, value: @researcher.phone_number, data: { behavior: 'validate_researcher_password', user_type: 'researcher' }
            = f.input :password_confirmation, required: true, data: { behavior: 'validate_password_confirmation', password_source: '#researcher_password', user_type: 'researcher' }

            .mt-3
            .px-xl-5
              .form-group.boolean.optional.researcher_tos style="margin-top: -20px;" class="#{ 'has-danger' if @researcher.errors.messages[:tos].present? }"
                input name="researcher[tos]" type="hidden" value="0" /
                input#researcher_tos.boolean.optional.filled-in.form-control.form-check-input name="researcher[tos]" type="checkbox" value="1" /
                label.boolean.optional.control-label for="researcher_tos" style=("transition: 0.2s; opacity: 1;")  Tos
                label.researcher_tos_label.en for='researcher_tos'
                  = t('researcher_account_activation.tos_html_en').html_safe
                - if @researcher.errors.messages[:tos].present?
                  = f.error :tos

            = f.input :reset_password_token, input_html: {value: params[:reset_password_token] || @researcher.reset_password_token }, as: :hidden
            = f.hidden_field :data_confirmed, value: true

            .text-xs-left.mt-2
              = f.submit "#{t('researcher_account_activation.activate')}", class: 'btn btn-success mt-2 mb-3', data: { behavior: 'spinner_loader' }
