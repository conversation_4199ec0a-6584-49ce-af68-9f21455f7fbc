- if resource.has_paid_visits?
  h3 <PERSON>jent ma opłacone wizyty
- else
  h3 <PERSON>jent nie ma opłaconych wizyt

= simple_form_for @form, url: change_project_visit_template_admin_clinical_user_path(resource) do |f|
  = f.input :project_visit_template_id, collection: resource.project.project_visit_templates.all, selected: resource.project_visit_template.id
  = f.hidden_field :clinical_user_id, value: resource.id
  = f.submit 'Zmień harmonogram'