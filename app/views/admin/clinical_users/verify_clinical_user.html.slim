scss:
  .checkboxes {
    .controls {
      display: inline;
    }
    label {
      margin-right: 5px !important;
    }
  }
.panel
  h3 Uzupelnij dane uczestnika:
  .panel_contents
    = simple_form_for resource, url: update_verify_clinical_user_admin_clinical_user_path(resource) do |f|
      = f.hidden_field :project_id, value: resource.project_id
      = f.input :clinical_center_id, collection: resource.project.clinical_centers, :include_blank => true, label_method: :to_s
      = f.input :patient_code
      = f.input :first_name
      = f.input :last_name
      = f.input :sex, collection: [["Male", ClinicalUser::MALE], ["Female", ClinicalUser::FEMALE ]], include_blank: true
      = f.input :provided_data_form_type, collection: ClinicalUser::PROVIDED_DATA_FORM_TYPE, include_blank: true
      = f.input :street
      = f.input :city
      = f.input :zip_code
      = f.input :phone_number
      = f.input :second_phone_number
      = f.input :account_number
      = f.input :bank_account_sec_code
      = f.input :project_visit_template_id, collection: resource.project.project_visit_templates.map{|x| ["#{x.name} #{x.visible? ? nil : ' (Ukryta)'}", x.id]}, include_blank: true
      = f.input :transfer_destination, collection: ClinicalUser::TRANSFER_DESTINATIONS
      br
      .checkboxes
        = f.input :perm_data, as: :boolean, label: "Zgoda na przetwarzanie danych osobowych / dostarczono formularz osobowy"
        = f.input :transfers_to_clinical_center, label: 'Transfer srodkow na konto osrodka', as: :boolean
        = f.input :perm_clinical_adv, label: "Zgoda na otrzymywanie informacji o przyszłych badaniach klinicznych", as: :boolean
        = f.input :sms_notifications, label: "Zgoda na otrzymywanie powiadomień SMS", as: :boolean
        = label_tag 'sms_confirmation', "Wygeneruj i wyślij haslo wraz z kodem pacjenta SMSem"
        = check_box_tag 'sms_confirmation', '1', true
      br
      = f.input :comments
      = f.submit "Zapisz uczestnika"
