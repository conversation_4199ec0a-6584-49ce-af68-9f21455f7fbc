

<div id="main_content">
    <div class="columns">

      <div class="column" style="width: 100%; margin-right: 2%;">
        <div class="panel">
          <h3>Dodaj wizytę z szablonu </h3>
          <div class="panel_contents">
            <h2> Lista dostępnych szablonów dla pacjenta: </h2>

				<% @templates.each do |pvt| %>
					<div style="border:1px solid gray; margin-bottom:5px">
						<h3><b>
							<%= pvt.name %> <%= " (Ukryta)" unless pvt.visible %> @ <%= "#{I18n.l pvt.updated_at, :format => :long }" %>
							<%= link_to "Dodaj wszystkie wizyty",
									add_visit_from_template_admin_clinical_user_path(params[:id],
									:project_visit_template => pvt.id) %>

						</b><h3/>
						<ul>
							<% pvt.visit_templates.sort_by(&:position).each do |vt| %>
								<li>
									<%= vt.name %>
									<%= link_to "Dodaj",
									add_visit_from_template_admin_clinical_user_path(params[:id],
									:visit_template => vt.id) %>
                  <%= link_to "Dodaj te i kolejne wizyty", add_visit_and_following_from_template_admin_clinical_user_path(params[:id],
                  :visit_template => vt.id) %>
									<%= link_to "Edytuj szablon wizyty", edit_admin_visit_template_path(vt.id) %>
								</li>
							<% end %>
						</ul>

					</div>


				<% end %>

          </div>
        </div>
      </div>
    </div>
    <div style="clear:both;"></div></div>
  </div>
