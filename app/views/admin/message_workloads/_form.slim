- if message
  p Ticket# #{message.ticket_number}
  p Email od: #{message.receiver.try(:email)}
  p Projekt: #{message.project}
  p Site: #{message.clinical_center}
  p SLA badania: #{message.fee_plan_short}
  p <PERSON><PERSON><PERSON><PERSON>: #{message.body}
= simple_form_for [:admin, resource] do |f|
  = f.association :resource, collection: Message.all, selected: message
  = f.hidden_field :resource_type, value: 'Message'
  = f.association :workload
  = f.hidden_field :added_by_type, value: 'AdminUser'
  = f.hidden_field :added_by_id, value: current_admin_user.id
  br
  = f.submit