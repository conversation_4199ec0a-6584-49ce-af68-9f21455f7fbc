.panel
  h3 Projekty i ośrodki
  .panel_contents
    - resource.projects.order('clinical_protocol_code asc').each.with_index do |project, i|
      span style="#{'background-color: #3180f7; color: white;' unless project.project_visit_templates.exists?}" #{ i + 1 }. #{link_to "#{ project.clinical_protocol_code } [#{ project.project_code }] - #{ project.fee_plan.try(:text) } (#{project.project_visit_templates.pluck(:name).to_sentence}) - #{ project.name }#{ ' CLOSED' if project.closed } #{project.currency}", admin_project_path(project), style: "#{ 'color: red;' if project.closed } #{ 'color: white;' unless project.project_visit_templates.exists?}"}
      - if project.clinical_centers.any?
        ul style="padding-left: 50px;"
          - project.clinical_centers.order('clinical_center_code asc').each do |cc|
            li = link_to "#{ cc.clinical_center_code } - #{ cc.name } (#{ cc.city })", admin_clinical_center_path(cc)
