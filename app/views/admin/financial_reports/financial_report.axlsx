wb = xlsx_package.workbook
wb.add_worksheet(name: '<PERSON>twar<PERSON> miesiąca') do |ws|
  ws.add_row ['Saldo na dzień', @report.start_date]
  ws.add_row ['Projekt/Nota', 'Saldo']
  @report.projects.each do |project|
    ws.add_row [project.clinical_protocol_code, (project.project_saldo_on_day(@report.start_date) || 0)]
  end
  ws.add_row ['Suma sald', @report.starting_date_projects_balance_total]
end

@report.projects.each do |project|
  wb.add_worksheet(name: project.clinical_protocol_code) do |ws|
    ws.add_row ['ID', 'Data', 'Sponsor/Kod', 'Zestawienie', '<PERSON><PERSON><PERSON> [zł]', '<PERSON><PERSON> [zł]']
    @report.all_transfers_for(project).each do |transfer|
      ws.add_row @report.transfer_details(transfer)
    end
  end
end

wb.add_worksheet(name: 'Zamknięcie miesiąca') do |ws|
  ws.add_row ['Saldo na zamknięcie', @report.end_date]
  ws.add_row ['Projekt / Nota', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON> zasileń', '<PERSON><PERSON> ob<PERSON>', '<PERSON>czba obciążeń', 'Liczba operacji', 'Saldo']
  @report.projects.each do |project|
    ws.add_row @report.closing_report_details_for(project)
  end
  ws.add_row ['Suma sald:', @report.final_balance_sum]
end