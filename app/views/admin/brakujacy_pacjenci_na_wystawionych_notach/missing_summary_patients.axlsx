wb = xlsx_package.workbook

wb.add_worksheet(name: '<PERSON><PERSON><PERSON><PERSON><PERSON>') do |ws|
  ws.add_row ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Numer Pacjenta", "Kwota należna", "CRO", "Numer noty obciążeniowej"]
  @clinical_users.each do |cu|
    ws.add_row [cu.clinical_protocol_code, cu.clinical_center_code, cu.patient_code, cu.transfers_in_debits(cu.project_debits.ids.uniq).distinct.sum(:amount), cu.cro_name, cu.project_debit_summaries.pluck(:note_number).uniq]
  end
end