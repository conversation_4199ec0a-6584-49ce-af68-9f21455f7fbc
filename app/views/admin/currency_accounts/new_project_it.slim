= simple_form_for @internal_transfer, url: create_project_it_admin_currency_account_path(resource) do |f|
  = f.input :currency_type, collection: InternalTransfer::ToProject::CURRENCY_TYPES.map { |k,v| [v, k] }, label: 'Wal<PERSON>', include_blank: false
  = f.input :amount
  = f.input :destination_currency_account_id, collection: @project_accounts, label: 'Konto projektu', label_method: proc { |r| "#{ r.clinical_protocol_code }/#{ r.currency }: #{ r.account_number }" }
  = f.hidden_field :destination_amount

  - if params['show_summary'].present?
    #transaction_summary
      h3 Podsumowanie transakcji
      p Kurs średni #{ @internal_transfer.source_currency }/#{ @internal_transfer.destination_currency }: #{ @internal_transfer.currency_rate&.rate || 1 }
      p Kurs średni #{ @internal_transfer.source_currency }/#{ @internal_transfer.destination_currency } ze spreadem #{ @internal_transfer.currency_spread_rate * 100 }%: #{ (@internal_transfer.rate_with_spread) }
      p Z konta <PERSON>ódłowego #{ @internal_transfer.source.name } #{ @internal_transfer.source.account_number } zostanie pobrana kwota: #{ number_to_currency @internal_transfer.source_amount } #{ @internal_transfer.source.currency }
      p Na konto docelowe projektu #{ @internal_transfer.destination.clinical_protocol_code } zostanie przesłana kwota: #{ number_to_currency @internal_transfer.destination_amount } #{ @internal_transfer.destination.currency }

    br
    br
    = f.submit 'Wykonaj przelew'

  br
  br
  = f.submit 'Pokaż podsumowanie', name: 'show_summary'