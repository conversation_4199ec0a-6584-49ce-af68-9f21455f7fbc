= simple_form_for @form, url: partial_credit_summary_admin_currency_account_path(resource) do |f|
  h3 Saldo: #{ number_to_currency resource.balance } #{ resource.currency_account_currency }

  = f.input :project_debit_summary_id, collection: @project_debit_summaries, label_method: proc { |pds| "#{pds.note_number} #{ pds.credit? ? '(credit)' : '' } --- #{number_to_currency pds.saldo, unit: pds.cro_currency}" }, label: 'Nota'
  = f.input :amount, label: '<PERSON>wota kredytu'
  = f.submit 'Dalej', style: 'margin-top: 1rem;'
