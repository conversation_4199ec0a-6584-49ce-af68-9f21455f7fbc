- pvts = resource.project.project_visit_templates.not_hidden.order('position ASC').map { |x| "<option value=#{x.id}>#{x.name}</option>" }.join

= simple_form_for @form, url: [:create_shipment, :admin, resource] do |f|
  = f.input :received_date, label: 'Data', as: :date, default: Date.today
  = f.input :cu_codes, label: '<PERSON><PERSON><PERSON><PERSON>', input_html: { data: { codes: resource.clinical_users.pluck(:patient_code) } }
  br
  #new_cu_templates data-pvts=pvts
  br
  br
  = f.submit


coffee:
  input = $('#clinical_center_shipment_form_cu_codes')
  codes = input.data('codes')
  new_cu_templates = $('#new_cu_templates')
  pvt_options = new_cu_templates.data('pvts')

  new_code= (val) ->
    val not in codes

  add_template = (val) ->
    html = "<div id=pvt_for_#{val}>" + '<p>Harmonogram dla ' + val + '</p><select name="clinical_center_shipment_form[project_visit_template_ids][' + val + ']">' + pvt_options + '</select></div>'
    new_cu_templates.append html

  remove_template = (val) ->
    $("#pvt_for_#{val}").remove()


  input.select2
    tags: codes
  .on("select2-selecting", (e) ->
    val = e.val
    if new_code(val)
      add_template(val)
  ).on("change", (e) ->
    if e.removed
      removed_val = e.removed.id
      remove_template(removed_val)
  )
