wb = xlsx_package.workbook
padded_number = wb.styles.add_style :format_code => "00###"
bold = wb.styles.add_style b: true

wb.add_worksheet(name: 'Podsumowanie') do |ws|
  ws.add_row ['Numer pacjenta', '<PERSON>umer protokołu', '<PERSON><PERSON><PERSON> cro', 'Data ostatniego przelewu', 'Liczba platnosci przechowywanych', 'Lączna kwota przechowywanych srodkow']
  clinical_users.each do |cu|
    last_transfer = cu.clinical_transfers.order('status_change_date desc').first
    transfers_size = cu.clinical_transfers.internal_transfers.size + cu.clinical_transfers.processing_outgoing.size
    transfers_sum = cu.clinical_transfers.internal_transfers.sum(:amount) + cu.clinical_transfers.processing_outgoing.sum(:amount)

    ws.add_row [
      cu.patient_code,
      cu.clinical_protocol_code,
      cu.contract_research_organization,
      last_transfer&.status_change_date || 'Brak',
      transfers_size,
      transfers_sum
    ]
  end
end