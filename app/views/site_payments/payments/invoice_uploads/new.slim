.row
  .col-md-8.col-sm-10.col-xs-12
    = section_header(title: "Site Payments", subtitle: "Upload Invoice")

.tab-content.vertical_1.mt-1
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      = simple_form_for @payment, html: { multipart: true }, url: site_payments_payment_invoice_uploads_path(@payment), method: :post do |f|
        = f.input :invoice_number
        = render Forms::UploadBtnComponent.new(name: 'site_payments_payment[invoice]', id: 'site_payments_payment_invoice')
        br
        = f.submit 'Upload', class: 'submit_btn'