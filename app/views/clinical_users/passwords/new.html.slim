scss:
  body.passwords img.clinical_img {
    display: initial;
  }

  #title_1 {
    font-size: 2em;
    display: block;
    margin-top: 0px;
    color: white;
    margin-top: 10px;
    font-weight: 500;
    text-transform: uppercase;
    margin-bottom: -10px;
  }

  button#sign_in, .slide_out_area {
    display: none;
  }

  #title_2 {
    font-size: 1.2em;
    display: block;
    margin-top: 0px;
    color: white;
    margin-top: 0px;
    line-height: 1.2
  }

  .forgot_password {
    color: #cce6e8;
    margin-top: -15px;
    margin-bottom: 0;
    font-size: .8em;
    text-align: right;
  }

  .form-group {
    margin-bottom: 0;
  }

  .form-group.password.optional.clinical_user_password {
    margin-bottom: 1rem;
  }


= image_tag 'payclinical-logo-2.png', class: 'clinical_img'

#sign_in_form_container
  .container style=("max-width: 50%;")
    span#title_1.mt-1 style='font-size: 2.5em;'
      | Konto pacjenta
    span#title_2
      | <PERSON><PERSON><PERSON> hasła

    .mt-2
    = simple_form_for(@clinical_user, :as => resource_name, :url => password_path(resource_name), defaults: {wrapper: :vertical_md_form})  do |f|
      = f.input :phone_number, input_html: { class: 'form-control no_phone_validation' }, label: "Telefon komórkowy"
      = f.input :patient_code, input_html: { class: 'form-control' }, label: "Numer pacjenta"
      .summary_section.default.mt-2.mb-0 style=("background-color: #ffffffab;background-color: #ffffff26;font-size: .90em;padding: 7px;line-height: 1.2;color: #ffffff;")
        | Poniżej proszę wpisać 6 ostatnich cyfr Państwa rachunku bankowego które przekazali Państwo na formularzu danych osobowych w ośrodku.
      = f.input :account_number, input_html: { class: 'form-control' }, label: '6 ostatnich cyfr rachunku bankowego'
      .text-xs-left.mt-3
        button.btn.submit_btn Wyślij hasło SMSem
