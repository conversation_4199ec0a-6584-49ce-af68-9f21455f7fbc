<% content_for :head do %>
    <style>
        .form-horizontal .controls {
            margin-left: 140px;
        }
        .form-horizontal .control-label {
            width: 140px;
            text-align: left;
        }
        .form-horizontal .form-actions {
            padding-left: 273px;
        }
    </style>
<% end %>
<div class="page-header">
  <h1><%= t('.text1') %></h1>
</div>

<%= form_for(@researcher, :url => { :action => "update_password" }, :html=> {:class=>'form-horizontal'} ) do |f| %>

    <%- if @researcher.errors.any? %>
      <div class="alert fade in alert-alert %>" >
        <a class="close" data-dismiss="alert" href="#">×</a>
        <%= @researcher.errors.full_messages.first %>
      </div>
    <%- end %>

    <div class="control-group">
      <%= f.label :password, :class=>'control-label' %>
      <div class="controls">
        <%= f.password_field :password, :autocomplete => "off"  %>
      </div>
    </div>


    <div class="control-group">
      <%= f.label :password_confirmation, :class=>'control-label' %>

      <div class="controls">
        <%= f.password_field :password_confirmation %>
      </div>
    </div>

    <div class="control-group">
      <div class="controls">
        <%= f.submit t('.change'), :class=> 'btn btn-success' %>
        <p style="margin-top: 10px;"><%= link_to t('.back'), edit_researcher_path(@researcher) %></p>

      </div>
    </div>

<% end %>

