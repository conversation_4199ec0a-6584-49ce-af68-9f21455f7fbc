= raw t('emails.to_researchers.failed_login_attempt.info', attempt_number: @attempt_number, date: @date, time: @time, location_string: @location_string)
- if @include_lock_acc_link
  = raw t('emails.to_researchers.failed_login_attempt.lock_acc_section', lock_acc_link: link_to(t('emails.to_researchers.failed_login_attempt.lock_acc_link'), new_v2_sponsor_account_lock_url(lock_token: @researcher.lock_token)))
= raw t('emails.to_researchers.failed_login_attempt.disable_notifications_section', disable_notifications_link: link_to(t('emails.to_researchers.failed_login_attempt.disable_notifications_link'), v2_sponsor_email_notifications_settings_url))
br
