scss:
  a.default_sm_btn {
    color: #00afff !important;

    &:hover {
      color: white !important;
    }
  }

  a.default_sm_btn.new_vtp {
    background-color: white ;
    margin-left: 10px;
  }

  .nested-fields.has-danger {
    display: block;
  }

  .nested-fields.has-danger, .names_and_descriptions_row.has-danger {
    background-color: #ffecf0;

    &.error_message {
      padding-top: 5px;
      padding-bottom: 5px;
      color: #d9534f;
    }
  }

  .names_and_descriptions_row.has-danger {
    margin-bottom: 15px;
  }

= section_header(title: "NEW STUDY SCHEDULE", subtitle: "Protocol# #{ red_project_link(project: @project) }")
.mt-1

.tab-content.vertical_1.card
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      .row
        .col-md-12
          = simple_form_for @form, url: v2_sponsor_project_project_visit_templates_path(@project, @clinical_user_params), html: { data: { behavior: 'project_visit_template_form' } } do |f|
            .row.mt-2
              = f.input :schedule_name, label: 'Schedule name', wrapper_html: {class: 'col-md-4 col-xs-12'}
            .mt-1
            #names_descriptions_rows
              - @form.valid_visit_names.each do |i, name|
                = render 'names_descriptions_row', i: i, f: f, visit_name: @form.value_for_index(index: i, attr: 'visit_names'), description: @form.value_for_index(index: i , attr: 'descriptions'), error: true


            .mt-2
            .summary_section.total_amount style='position: relative; margin-top: 0'
              a.default_sm_btn.new_vtp href='#' data-behavior='add_visit_name_and_description_row' data-template="#{ CGI.escapeHTML(render('names_descriptions_row', i: 0, f: f, description: '', visit_name: '', error: false)).html_safe }" Add a new visit

              label style=("margin-left: 10px;")
                | Click to add a new visit to the payment schedule.


            .mt-3
            = f.submit 'Save', class: 'submit_btn'
            = link_to_back(text: 'Back', link_class: 'btn btn-warning cancel-left-margin')
