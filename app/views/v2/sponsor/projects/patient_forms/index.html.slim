- default_patient_form_version = '4.0'
- default_patient_form_dated = '24-05-2018'

- if @clinical_center
  - subtitle = "#{ link_to_cc(clinical_center: @clinical_center) } Protocol# #{ red_project_link(project: @project) }"
- else
  - subtitle = "Protocol# #{ red_project_link(project: @project) }"

= section_header(title: "SUBJECTS' DATA FORMS", subtitle: subtitle)
.mt-1


.tab-content.vertical_1.card
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      .row
        .col-xs-12
          table.table
            thead
              tr
                th.text-xs-left Document name
                th.text-xs-center.fit_to_content style="min-width: 130px"  Version
                th.text-xs-left.fit_to_content style="min-width: 130px" Dated
                th.text-xs-center.fit_to_content
            tbody
              - if @clinical_center
                - if @patient_forms.empty?
                  tr
                    td.text-xs-left
                      = link_to 'Subject’s personal data form', v2_sponsor_patient_form_path(0, clinical_center_code: @clinical_center.try(:clinical_center_code), clinical_protocol_code: @project.clinical_protocol_code, sponsor_name: @project.name), target: :_blank
                    td.text-xs-center = default_patient_form_version
                    td.text-xs-left = default_patient_form_dated
                    td
                      = link_to 'Download', v2_sponsor_patient_form_path(0, clinical_center_code: @clinical_center.try(:clinical_center_code), clinical_protocol_code: @project.clinical_protocol_code, sponsor_name: @project.name), class: 'default_sm_btn', target: :_blank
                - @patient_forms.each do |pf|
                  tr
                    td = link_to pf.name, v2_sponsor_patient_form_path(pf.id, clinical_center_code: @clinical_center.try(:clinical_center_code), clinical_protocol_code: @project.clinical_protocol_code, sponsor_name: @project.name), target: :_blank
                    td.text-xs-center = pf.version
                    td = l pf.dated if pf.dated
                    td = link_to 'Download', v2_sponsor_patient_form_path(pf.id, clinical_center_code: @clinical_center.try(:clinical_center_code), clinical_protocol_code: @project.clinical_protocol_code, sponsor_name: @project.name), class: 'default_sm_btn', target: :_blank
              - else
                - @project.clinical_centers.each do |cc|
                  tr
                    td.td_style1 colspan='10'
                      a href='#' data-behavior='slide_up_trigger' data-target=".cc_#{ cc.id }_tr"
                        | Site #{ cc.clinical_center_code }
                        i.fa.fa-angle-down.rotate-icon
                  tr class="cc_#{ cc.id }_tr"
                    - if @patient_forms.empty?
                      td
                        = link_to 'Subject’s personal data form', v2_sponsor_patient_form_path(0, clinical_center_code: cc.try(:clinical_center_code), clinical_protocol_code: @project.clinical_protocol_code, sponsor_name: @project.name), target: :_blank
                      td.text-xs-center = default_patient_form_version
                      td = default_patient_form_dated
                      td = link_to 'Download', v2_sponsor_patient_form_path(0, clinical_center_code: cc.try(:clinical_center_code), clinical_protocol_code: @project.clinical_protocol_code, sponsor_name: @project.name), class: 'default_sm_btn', target: :_blank

                    - @project.patient_forms.each do |pf|
                      tr
                        td = link_to pf.name, v2_sponsor_patient_form_path(pf.id, clinical_center_code: cc.try(:clinical_center_code), clinical_protocol_code: @project.clinical_protocol_code, sponsor_name: @project.name), target: :_blank
                        td.text-xs-center = pf.version
                        td = l pf.dated if pf.dated
                        td = link_to 'Download', v2_sponsor_patient_form_path(pf.id, clinical_center_code: cc.try(:clinical_center_code), clinical_protocol_code: @project.clinical_protocol_code, sponsor_name: @project.name), class: 'default_sm_btn', target: :_blank

          = paginate @patient_forms
          .mt-3
          = link_to_back(text: 'Back', link_class: 'btn btn-warning margin0')
