- if @account_activities.empty?
  p No activity has been recorded yet.
- else
  table.table.table-striped
    thead
      tr
        th.text-xs-left.date_column Date
        th.text-xs-left style=("width: auto") Activity
    tbody
      - @account_activities.each do |aa|
        - unless aa.description.nil?
          tr
            td.text-xs-left = two_line_date_time(aa.created_at)
            td.text-xs-left = aa.description.html_safe

  = paginate @account_activities
