- hidden = local_assigns[:hidden] || false
tr class="#{ tr_class }" style="#{ 'display: none;' if hidden }"
  td = link_to cc.clinical_center_code, v2_sponsor_clinical_center_path(cc)
  td = link_to string_or_na(string: cc.name), v2_sponsor_clinical_center_path(cc)
  td = string_or_na(string: cc.supervisor_full_name)
  td = string_or_na(string: cc.city)
  td
    = dropdown_btn do
      - has_access = cc.researchers.map(&:id).include?(current_researcher.id)
      = link_to "Subjects' forms", v2_sponsor_project_patient_forms_path(cc.project, clinical_center_id: cc), class: 'dropdown-item'
      = link_to 'Site information', v2_sponsor_clinical_center_path(cc), class: "dropdown-item #{'disabled' unless has_access }"
      = toggle_cc_link(clinical_center: cc, klass: "#{'disabled' unless has_access}")

