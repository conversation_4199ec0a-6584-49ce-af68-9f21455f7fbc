= section_header(title: "Account settings", subtitle: subject_protocol_link(cu: @clinical_user))

-if @clinical_user.closed
  .row
    .col-xs-12 style=("float: none;float: initial")
      .summary_section.danger
        = clinical_user_closed_details(clinical_user: @clinical_user)
.mt-2

.tab-content.vertical_1.card
  #panel1.tab-pane.active role="tabpanel"
    .container
      = simple_form_for @clinical_user, url: '#' do |f|
        .col-xs-12
          .row.mt-1
            = f.input :first_name, as: :string, input_html: { value: dummy_masked_text }, disabled: true
            = f.input :last_name, as: :string, input_html: { value: dummy_masked_text }, disabled: true
            = f.input :street, as: :string, input_html: { value: '*' * 12 }, disabled: true
            = f.input :city, as: :string, input_html: { value: '*' * 12 }, disabled: true
            = f.input :zip_code, as: :string, input_html: { value: '*' * 6 }, disabled: true
            .mt-4
            = f.input :phone_number, as: :string, label: 'Mobile phone', input_html: { value: masked_text(text: @clinical_user.phone_number, show_last_chars: 3) }, disabled: true
            = f.input :account_number, as: :string, input_html: { value: subject_acc_nr(clinical_user: @clinical_user) }, disabled: true, label: "Subject's bank account"
            = f.input :project_visit_template, as: :string, label: 'Payment schedule', disabled: true
            = f.input :transfer_destination, disabled: true, input_html: { value: t("activerecord.attributes.clinical_user.transfer_destinations.#{ @clinical_user.transfer_destination }") }
            .mt-3

            = link_to_back(text: 'Back', link_class: 'btn btn-warning margin0')
            / = cu_deactivate_link(cu: @clinical_user, researcher: current_researcher)
