.confirmed_post_and_cc_summary
  = t("modals.transfer.will_order_transfer_confirmed_post_and_cc", amount: @view.visit_amount, patient_code: @clinical_user.patient_code)
  select.md-select#visit_transfer_destination_select name="transfer_to"
    option selected="" value="clinical_user_post" data-acc-nr=""  = t("modals.transfer.to_patient_by_post")
    option value="clinical_center" data-acc-nr="#{ @clinical_user.clinical_center.formatted_account_number }" = t("modals.transfer.to_clinical_center_acc")

  span#transfer_to_account .
