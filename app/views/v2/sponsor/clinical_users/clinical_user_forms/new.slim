.row
  .col-xs-12
    = section_header(title: "UPLOAD DATA FORM FOR #{ red_link(text: @clinical_user.patient_code, path: v2_sponsor_clinical_user_visits_path(@clinical_user)) }", subtitle: "SITE# #{ red_link(text: @clinical_user.clinical_center_code, path: v2_sponsor_clinical_center_clinical_users_path(@clinical_user.clinical_center)) } PROTOCOL# #{ red_link(text: @clinical_user.clinical_protocol_code, path: v2_sponsor_project_path(@clinical_user.project)) }", klass: 'no_bottom_margin')
.mt-1

.tab-content.vertical_1.card
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      .row
        .col-sm-12
          .summary_section.warning.mt-0.mb-2
            | Please select a scanned personal data form for subject #{ @clinical_user.patient_code } and upload it to PayClinical. These data are strictly CONFIDENTIAL and not accessible by other users.

          = simple_form_for [:v2, :sponsor, @clinical_user, @clinical_user_form] do |f|
            .row
              .col-md-12
                #filename
                .fileinput.text-center.fileinput-new data-provides="fileinput"
                  span.btn.btn-warning.btn-file style=("height: 37px; margin-left: 0; width: 200px; margin-top: 12px; margin-left: -1px;")
                    span.fileinput-new Select file
                    span.fileinput-exists Upload a new one

                    .avatar_input_hide
                      = f.input :file, as: :file, wrapper: false, label: false
                    input name="[file][avatar]" id="form_avatar" type="file"

            .row
              .col-md-12
                = f.submit 'Upload', class: 'submit_btn'


coffee:
  $ ->
    $("#form_avatar").on 'change', (e) ->
      val = $(this).val().split("\\")
      val = val[val.length - 1]
      $("#filename").text(val)
