= section_header(title: "Subjects WITHOUT data forms", subtitle: "Reported for: #{ link_to 'ALL TRIALS', v2_sponsor_projects_path, class: 'red hover_orange' }")
.mt-1

.tab-content.vertical_1.card
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      table.table.table-stripped
        thead
          tr
            = render 'shared/clinical_users_table_head_fields', checkbox_disabled: false
        tbody
          - if @project_cc_cu.blank?
            tr
              td colspan='99' You are not authorized to access any projects.
          - @project_cc_cu.each do |p_cc_v|
            - project = p_cc_v[0]
            - p_cc_v[1].each do |cc, cu|
              = render partial: 'shared/cc_tr_header', locals: { clinical_center: cc, cras_list: true, project_context: true, project: project }
              - cu.each do |cu|
                = render 'shared/clinical_users_table_cu_tr', cu: cu, cc: cc, full_load: true
      = link_to_back(text: 'Back', link_class: 'btn btn-warning margin0 mt-2')