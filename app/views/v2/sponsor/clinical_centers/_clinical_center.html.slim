.col-xs-6.col-sm-4
  .card.clinical_center_card
    .card-block
      h4.card-title = clinical_center.clinical_center_code
      - if clinical_center.closed
        span.tag.tag-danger Closed
      p.card-text Some quick example text to build on the card title and make up the bulk of the card's content.
      = link_to 'Profile', v2_sponsor_clinical_center_path(clinical_center), class: 'btn btn-primary'
      - if !clinical_center.closed
        = link_to 'Close site', new_v2_sponsor_clinical_center_clinical_center_closure_path(clinical_center), class: 'btn btn-danger'
