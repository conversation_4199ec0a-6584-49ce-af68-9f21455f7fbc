$ ->
  timer = undefined
  $('[data-behavior="account_activity_ajax_search"]').on 'keyup', (e) ->
    project_id = $(@).data('project-id')
    clearTimeout timer
    timer = setTimeout((->
      target_input = e.target
      term = target_input.value
      path = "/v2/sponsor/projects/#{ project_id }/account_activity_logs"
      $.ajax
        url: path
        data: { q: term }
        dataType: 'script'
    ), 1000)
