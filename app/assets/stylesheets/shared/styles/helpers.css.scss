.center_block {
  display: table;
  margin: auto;
}

.red, .red_color {
  color: $main-red-color;
}

.green_color {
  color: $main-green-color;
}

.orange_color {
  color: $main-orange-color;
}

.yellow_color {
  color: $main-yellow-color;
}

.blue_color {
  color: $main-light-blue-color;
}

.green_background {
  background-color: $main-green-color !important;
}

.red_background {
  background-color: $main-red-color !important;
}

.hover_yellow {
  &:hover {
    color: $main-yellow-color !important;
  }
}

.hover_red {
  &:hover {
    color: $main-red-color !important;
  }
}

span.amount_positive_val {
  color: $btn-success-color;
  &:before {
    content: '+'
  }
}

span.amount_negative_val {
  color: $main-red-color;
  &:before {
    content: '-'
  }
}

a.hover_red {
  &:hover {
    color: $main-red-color;
  }
}

.tag-outer {
  color: white;
  font-size: 22px;
  .badge {
    padding: 5px;
  }
}

.opacity-0 {
  opacity: 0;
}

.hover_orange {
  &:hover {
    color: $main-orange-color;
  }
}

.hover_blue {
  cursor: default;
  &:hover {
    color: $main-blue-color;
  }
}

.blue_color {
  color: $main-blue-color;
}

.default_font {
  font-family: $default-font;
}

.font-size-1 {
  font-size: 1em;
}

.font-size-1-2 {
  font-size: 1.2em;
}

.black_color {
  color: black;
}

.top_1_rem_important {
  top: 1rem !important;
}

.gray {
  color: $main-light-gray;
}

.h-100-px {
  height: 100px;
}

.text_crossed {
  text-decoration: line-through;
}

.four-lined-text {
  -webkit-box-orient: vertical;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}

.one-lined-text {
  -webkit-box-orient: vertical;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}