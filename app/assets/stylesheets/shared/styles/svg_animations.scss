svg {
  #kieszonkowe_svg_animate {
    stroke: #000;
    stroke-width: 3px;
    stroke-dasharray: 1500px;
    stroke-dashoffset: 1500px;

    fill: #ff000000;

    &.animate {
      animation-name: draw_and_fill;
      animation-duration: 1.5s;
      animation-fill-mode: forwards; // Stay on the last frame
      animation-iteration-count: 1; // Run only once
      animation-timing-function: linear;
    }
  }
}

svg {
  #at3 {
    stroke: #004769;
    stroke-width: 2px;
    stroke-dasharray: 1500px;
    stroke-dashoffset: 1500px;

    fill: #ff000000;

    &.animate {
      animation-name: draw_and_fill_black;
      animation-duration: 1.5s;
      animation-fill-mode: forwards; // Stay on the last frame
      animation-iteration-count: 1; // Run only once
      animation-timing-function: linear;
    }
  }
}


svg {
  #custom_id {
    stroke: #000;
    stroke-width: 3px;
    stroke-dasharray: 3000px;
    stroke-dashoffset: 3000px;

    animation-name: draw;
    animation-duration: 5s;
    animation-fill-mode: forwards; // Stay on the last frame
    animation-iteration-count: 10; // Run only once
    animation-timing-function: linear;
  }
}

svg {
  #blue_heart {
    opacity: 0;

    &.animate {
      animation-name: display;
      animation-duration: .5s;
      animation-fill-mode: forwards;
      animation-iteration-count: 1;
      animation-timing-function: linear;
      animation-delay: .5s;
    }
  }

  #yellow_heart {
    opacity: 0;

    &.animate {
      animation-name: display;
      animation-duration: .5s;
      animation-fill-mode: forwards;
      animation-iteration-count: 1;
      animation-timing-function: linear;
    }
  }
}

@keyframes display {
  to {
    opacity: 1;
  }
}

@keyframes draw {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes draw_and_fill {
  0% {
    stroke-dashoffset: 1500px;
    fill: rgba(173, 27, 54, 0);

  }

  50% {
    stroke-dashoffset: 750px;
    fill: rgba(173, 27, 54, 0);

  }

  100% {
    stroke-dashoffset: 0;
    fill: rgba(173, 27, 54, 1);
  }
}

@keyframes draw_and_fill_black {
  0% {
    stroke-dashoffset: 1500px;
    fill: rgba(0, 113, 166, 0);;

  }

  50% {
    stroke-dashoffset: 750px;
    fill: rgba(0, 113, 166, 0);

  }

  100% {
    stroke-dashoffset: 0;
    fill: rgba(0, 113, 166, 1);
  }
}

#pomoc_heart {
  stroke-width: 3px !important;
  stroke: #e9442e !important;
}
