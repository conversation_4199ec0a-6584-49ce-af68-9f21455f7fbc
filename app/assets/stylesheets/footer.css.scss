* {
  margin: 0;
}
html, body {
  height: 100%;
}

.page-wrap {
  min-height: 100%;
  /* equal to footer height */
  margin-bottom: -60px;

  &:after {
    content: "";
    display: block;
    height: 60px;
  }
}


footer {
  position: relative;
//  width: 100%;
  height: 60px;
  background: #FFFFFF;
  text-align: center;
  z-index: 99;

  .credits {
    color: #55606A;
    font-size: 13px;
    margin-bottom: 0;
    padding: 20px 20px;
    text-transform: none;
    text-align: left;
    line-height: 1.2;
  }
}

footer.page-footer {
  .footer-bottom-section {
    padding-left: 0;
    padding-right: 0;
    margin-left: 0;
    margin-right: 0;
    padding-top: 10px;
    background-color: transparent;
    padding-bottom: 10px;
    border-top: 1px solid $main-dark-grey-color;
    position: relative;

    span.footer-norton-span {
      position: absolute;
      right: -10px;
      top: 0;
      width: 40px;
      height: 30px;
      right: -2px;
      top: -2px;
      z-index: 99;
    }

    img.footer-norton {
      height: 48px;
      position: absolute;
      right: -10px;
      top: -22px;
    }

    img.footer_logo {
      height: 40px;
      position: absolute;
      left: 0;
    }

    img.social_icon {
      margin-top: 2px;
      height: 37px;
      margin-right: 10px;
      position: absolute;
      right: 0px;

      &.fb {
        right: 48px;
      }
    }

    .footer-copyright {
      margin-left: 75px;
      text-align: left;
      background-color: transparent;
      color: black;
      height: initial;
      background-color: transparent;
      text-align: left;
      line-height: normal;
      line-height: initial;
      height: auto;
      height: initial;
      color: black;
    }

    .footer-policy {
      // background-color: red;
      margin-left: 75px;
      text-align: left;

      ul.footer-policy-links {
        display: inline-flex;
        margin-bottom: 0;
        height: 15px;
        line-height: .9;

        li {
          margin-right: 10px;
          padding-right: 10px;
          border-right: 2px solid #FDBB2F;

          &:last-child {
            border-right: none;
          }

          a {
            font-size: 1em;
            font-family: inherit;
            &:hover {
              color: $main-orange-color;
            }
          }
        }
      }
    }
  }
}

@keyframes gradient_animation_1 {
  0%{background-position:50% 50%}
  25%{background-position:100% 50%}
  75%{background-position:0% 50%}
  100%{background-position:50% 50%}
}

footer.page-footer.user-section-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 998;
  border: none;
  .footer-copyright {
    color: white;
  }

  padding-top: 0;
  background-color: #113b64 !important;

  background: rgb(69,108,120); /* Old browsers */
  // background: linear-gradient(to right, rgba(69,108,120,1) 0%,rgba(2,149,169,1) 35%,rgba(2,149,169,1) 50%,rgba(2,149,169,1) 65%,rgba(69,108,120,1) 100%);
  // background: linear-gradient(to right, #5d2f2f 0%, #0295a9 35%, #0295a9 50%, #0295a9 65%, #5d2f2f 100%);
  // background: linear-gradient(to right, #2f525d 0%, #0295a9 35%, #0295a9 50%, #0295a9 65%, #2f525d 100%);
  background: linear-gradient(to right, #113b64 0%, #113b64 35%, #0295a9 50%, #113b64 65%, #4892a2 100%);

  background-size: 200% 200%;
  background-position: 50% 50%;
  // animation: gradient_animation_1 40s ease-in-out infinite;
  // animation-delay: 2s;
}

.footer-bottom-section {
  &:before {
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height: 28px;
    opacity: .08;
    background-repeat: repeat-y;
    background: image-url('wektor-tla-3.jpg') 0% 0%;
    background-repeat: repeat-x;
    background-size: 120%;
    transition: .5s;
  }
}

footer.page-footer {
  height: auto;
}
