class CallsAndMeetings::BaseReport < Reports::BaseReport
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

  attr_accessor :call_meeting

  def initialize(call_meeting)
    super({ page_size: 'A4' })

    @call_meeting = call_meeting

    set_locale

    root_path = Rails.root
    font_families.update(
      "Source_Sans_Pro" => {
        normal: "#{root_path}/lib/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf",
        italic: "#{root_path}/lib/fonts/Source_Sans_Pro/SourceSansPro-Italic.ttf",
        light: "#{root_path}/lib/fonts/Source_Sans_Pro/SourceSansPro-Light.ttf",
        bold: "#{root_path}/lib/fonts/Source_Sans_Pro/SourceSansPro-SemiBold.ttf"
      }
    )
    font "Source_Sans_Pro", size: 14
    fill_color "000000"

    generate_pdf_content
  end

  private

  def header
    text "Let Me Pay Sp. z o.o.", size: 10, align: :right, style: :bold
    text '<PERSON>łżecka 26, 02-135 Warsaw', size: 10, align: :right

    bounding_box([0, cursor + 30], width: 400) do
      image "#{Rails.root}/public/pc-logo-dark.png", width: 170
    end
  end

  def footer
    page_count.times do |i|
      go_to_page i

      left_options = { at: [bounds.left, bounds.bottom], size: 10, width: 300, align: :left, height: 50 }
      text_box "Generated on #{Time.now.strftime("%d %b %Y %H:%M %Z")}", left_options

      right_options = { at: [bounds.right-150, bounds.bottom], size: 10, width: 150, align: :right, height: 50 }
      text_box "<EMAIL>", right_options
    end
  end

  def set_locale
    I18n.locale = :en
  end
end
