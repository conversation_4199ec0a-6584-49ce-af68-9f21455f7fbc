class Projects::PremiumStudyReportPdf < Reports::BaseReport
  include ActionView::Helpers::DateHelper
  include Visits<PERSON>elper

  attr_accessor :project, :clinical_centers, :start_date, :end_date, :table_of_contents, :site_statistics, :all_sites_statistics


  def initialize(params = {})
    super({ page_size: 'A4' })

    @project = params[:project]
    @clinical_centers = @project.clinical_centers.includes(:clinical_users, :primary_cra, :project_roles).order('clinical_center_code asc')
    @start_date = params[:start_date]
    @end_date = params[:end_date]

    # Table of contents: [ [page, name, anchor] ]
    @table_of_contents = []

    load_site_statistics

    root_path = Rails.root
    font_families.update(
      "Source_Sans_Pro" => {
        normal: "#{root_path}/lib/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf",
        italic: "#{root_path}/lib/fonts/Source_Sans_Pro/SourceSansPro-Italic.ttf",
        light: "#{root_path}/lib/fonts/Source_Sans_Pro/SourceSansPro-Light.ttf",
        bold: "#{root_path}/lib/fonts/Source_Sans_Pro/SourceSansPro-SemiBold.ttf"
      }
    )
    font "Source_Sans_Pro", size: 14
    fill_color "000000"

    I18n.locale = :en

    pdf_content
  end


  private

  def pdf_content
    header

    move_down(180)

    font "Source_Sans_Pro", size: 30
    text "SUBJECT REIMBURSEMENT"
    text "IN CLINICAL TRIALS"

    move_down(60)
    font "Source_Sans_Pro", size: 16
    text "<b>#{ project.clinical_protocol_code } STUDY</b>", inline_format: true
    move_down(10)
    font "Source_Sans_Pro", size: 14
    text "#{ cro.name }"
    move_down(3)
    text cro.company_street
    move_down(3)
    text "#{ cro.company_zip_code }, #{ cro.company_city }"

    move_down(240)
    font "Source_Sans_Pro", size: 16
    text "<b>SUMMARY REPORT</b>", inline_format: true
    text "Generated on #{Date.today.strftime("%d-%m-%Y")}"


    start_new_page
    clinical_sites_overview

    clinical_sites_subjects_table

    create_table_of_contents

    footer
  end

  def cro
    project.cro
  end

  def clinical_sites_overview
    anchor = "projects_overview"
    section_name = "#{@project.clinical_protocol_code} study overview"
    @table_of_contents << [page_number, section_name, anchor]

    make_header(anchor, section_name)

    move_down(15)
    font "Source_Sans_Pro", size: 12
    generate_overview_text
    move_down(15)

    table data_for_clinical_sites_table, header: true, width: 530 do |table|
      table.column(1).style align: :left
      table.column(1).style align: :center
      table.column(2..5).style align: :right
      table.column(6).style align: :center

      table.row(0).style align: :center, valign: :center, border_widths: [0, 0, 1, 0]
      table.row(0).font_style = :bold
      table.row(1..99999).style border_width: 0
      table.row(-1).style border_widths: [1, 0, 0, 0], border_colors: ['e02361' ,'ffffff' ,'ffffff' ,'ffffff' ]
      table.cells.style inline_format: true, valign: :center, size: 10
    end
  end

  def clinical_sites_subjects_table
    # grouped_sites = @clinical_centers.group_by { |site| site.clinical_users.any? }

    @clinical_centers.each do |site|
      start_new_page

      anchor = "site_#{site.id}"
      section_name = "Site #{site.clinical_center_code} (#{site.city}, #{site.country_name})"
      @table_of_contents << [page_number, section_name, anchor]

      make_header(anchor, section_name)

      move_down(15)
      font "Source_Sans_Pro", size: 12
      generate_site_text(site)
      move_down(15)


      table data_for_clinical_site_subjects_table(site), header: true, width: 530 do |table|
        table.column(0).style align: :left
        table.column(1..4).style align: :right
        table.column(5).style align: :center
        table.column(6).style align: :right

        table.row(0).style align: :center, valign: :center, border_widths: [0, 0, 1, 0]
        table.row(0).font_style = :bold
        table.row(1..99999).style border_width: 0
        table.row(-1).style border_widths: [1, 0, 0, 0], border_colors: ['e02361' ,'ffffff' ,'ffffff' ,'ffffff' ]
        table.cells.style inline_format: true, valign: :center, size: 10
      end

      subject_tables(site)
    end

    # if grouped_sites[false]
    #   start_new_page
    #   anchor = "sites_without_subjects"
    #   section_name = "Sites without active subjects' accounts"
    #   @table_of_contents << [page_number, section_name, anchor]

    #   make_header(anchor, section_name)

    #   # create table of content entries
    #   grouped_sites[false].each do |site|
    #     section_name = "Site #{site.clinical_center_code} (#{site.city}, #{site.country_name})"
    #     @table_of_contents << [page_number, "\xC2\xA0\xC2\xA0\xC2\xA0\xC2\xA0#{section_name}", '']
    #   end

    #   table data_for_clinical_sites_without_subjects_table(grouped_sites[false]), header: true, width: 530 do |table|
    #     table.row(0).style valign: :center, border_widths: [0, 0, 1, 0]
    #     table.row(0).font_style = :bold
    #     table.row(1..99999).style border_width: 0
    #     table.cells.style inline_format: true, valign: :center, size: 10, align: :left
    #   end
    # end
  end

  def subject_tables(site)
    start_new_page
    font "Source_Sans_Pro", size: 18

    section_name = "Site #{site.clinical_center_code} (#{site.city}, #{site.country_name}) subjects"

    text section_name

    site.clinical_users.includes(:user_action_requirements).each do |subject|
      move_down(40)
      anchor = "subject_#{subject.id}"
      @table_of_contents << [page_number, "\xC2\xA0\xC2\xA0\xC2\xA0\xC2\xA0\xC2\xA0\xC2\xA0\xC2\xA0\xC2\xA0Subject #{subject.patient_code}", anchor]
      add_dest(anchor, dest_xyz(0, y, nil, page))

      subject_visits_table(subject)
      subject_payments_table(subject)
      # move_down(10)
      # subject_actions_required_table(subject)
    end
  end

  def subject_visits_table(subject)
    font "Source_Sans_Pro", size: 14
    text "Subject #{subject.patient_code} visits"

    visits = subject.sorted_visits.select { |v| !v.state.in?(['n', 'u']) }

    if visits.blank?
      move_down(3)
      font "Source_Sans_Pro", size: 10
      text "There are no visits added to this account."
      font "Source_Sans_Pro", size: 14
    else
      table data_for_subject_visits_table(visits), header: true, width: 530 do |table|
        table.column(0).style align: :center, width: 65
        table.column(1).style align: :left
        table.column(2).style align: :center, width: 130
        table.column(3).style align: :right, width: 80
        table.column(4).style align: :right, width: 85

        table.row(0).style align: :center, valign: :center, border_widths: [0, 0, 1, 0]
        table.row(0).font_style = :bold
        table.row(1..99999).style border_width: 0
        table.row(-1).style border_widths: [1, 0, 0, 0], border_colors: ['e02361' ,'ffffff' ,'ffffff' ,'ffffff' ]
        table.cells.style inline_format: true, valign: :center, size: 10
      end
    end
  end

  def subject_payments_table(subject)
    payments = subject.clinical_transfers.joins(:visit).merge(Visit.applicable).not_master.not_cancelled

    return if payments.empty?

    move_down(20)
    font "Source_Sans_Pro", size: 14
    text "Subject #{subject.patient_code} payments sent"

    table data_for_subject_payments_table(payments), header: true, width: 530 do |table|
      table.column(0).style align: :center, width: 65
      table.column(1).style align: :right, width: 80
      table.column(2).style align: :center, width: 160
      table.column(3).style align: :left
      table.column(4).style align: :center, width: 65, inline_format: true

      table.row(0).style align: :center, valign: :center, border_widths: [0, 0, 1, 0]
      table.row(0).font_style = :bold
      table.row(1..99999).style border_width: 0
      table.cells.style inline_format: true, valign: :center, size: 10
    end
  end

  def subject_actions_required_table(subject)
    font "Source_Sans_Pro", size: 14
    text "Subject #{subject.patient_code} action items"

    actions = subject.user_action_requirements
    if actions.empty?
      move_down(3)
      red_notification('No action items recorded')
    else
      table data_for_subject_actions_required_table(subject.user_action_requirements), header: true, width: 530 do |table|
        table.column(0..1).style align: :left
        table.column(2).style align: :right

        table.row(0).style align: :center, valign: :center, border_widths: [0, 0, 1, 0]
        table.row(0).font_style = :bold
        table.row(1..99999).style border_width: 0
        table.cells.style inline_format: true, valign: :center, size: 10
      end
    end
  end

  def data_for_clinical_sites_without_subjects_table(sites_without_subjects)
    data = []
    data << ['Site']
    sites_without_subjects.each { |site| data << ["#{site.clinical_center_code} (#{site.city}, #{site.country_name})"] }
    data
  end

  def create_table_of_contents
    go_to_page(1)
    table_size = @table_of_contents.size
    rows_per_page = 32.0
    new_pages_required = (table_size / rows_per_page).ceil
    new_pages_required -= 1 if table_size % rows_per_page >= 30 # Account for 'Table of contents' header
    new_pages_required.times { start_new_page }
    go_to_page(2)

    font "Source_Sans_Pro", size: 20
    text "Table of contents"
    move_down(20)

    toc_data = @table_of_contents.map do |page, name, anchor|
      ["<link anchor='#{anchor}'>#{name}</link>", page + new_pages_required]
    end

    table toc_data, header: false, width: 530 do |table|
      table.column(0).style align: :left
      table.column(1).style align: :right

      table.row(0..99999).style border_width: 0
      table.cells.style inline_format: true, valign: :center, size: 12
    end
  end

  def data_for_clinical_sites_table
    data = []

    # Headers
    data << [
      "Site",
      "Enrolled<br>subjects",
      "Visits to be<br>reimbursed",
      "Awaiting<br>approval",
      "Payments<br>in process",
      "Payments<br>completed",
      "Action<br>items"
    ]

    @clinical_centers.each do |cc|
      # Add cc row
      data << [
        "#{cc.clinical_center_code}<br>(#{cc.city})",
        @site_statistics[cc.id][:cu_count].to_s,
        "#{@site_statistics[cc.id][:visits_planned]}<br>#{number_to_currency(@site_statistics[cc.id][:visits_planned_sum] , unit: '')} #{@project.currency}",
        "#{@site_statistics[cc.id][:visits_transfer_waiting]}<br>#{number_to_currency(@site_statistics[cc.id][:visits_transfer_waiting_sum] , unit: '')} #{@project.currency}",
        "#{@site_statistics[cc.id][:visits_transfer_in_progress]}<br>#{number_to_currency(@site_statistics[cc.id][:visits_transfer_in_progress_sum] , unit: '')} #{@project.currency}",
        "#{@site_statistics[cc.id][:visits_transfer_paid]}<br>#{number_to_currency(@site_statistics[cc.id][:visits_transfer_paid_sum] , unit: '')} #{@project.currency}",
        @site_statistics[cc.id][:cu_actions_count].to_s
      ]
    end

    # Add summary row
    data << [
      "All sites",
      @all_sites_statistics[:cu_count_total].to_s,
      "#{@all_sites_statistics[:visits_planned_total]}<br>#{number_to_currency(@all_sites_statistics[:visits_planned_sum_total] , unit: '')} #{@project.currency}",
      "#{@all_sites_statistics[:visits_transfer_waiting_total]}<br>#{number_to_currency(@all_sites_statistics[:visits_transfer_waiting_sum_total] , unit: '')} #{@project.currency}",
      "#{@all_sites_statistics[:visits_transfer_in_progress_total]}<br>#{number_to_currency(@all_sites_statistics[:visits_transfer_in_progress_sum_total] , unit: '')} #{@project.currency}",
      "#{@all_sites_statistics[:visits_transfer_paid_total]}<br>#{number_to_currency(@all_sites_statistics[:visits_transfer_paid_sum_total] , unit: '')} #{@project.currency}",
      @all_sites_statistics[:cu_actions_count_total]
    ]

    data
  end

  def generate_overview_text
    unless @project.debit_allowed?
      transactions = ProjectTransfers.call(project_id: @project.id, date_range: @project.created_at.to_date..Date.today)
      transactions_decorator = TransactionsDecorator.new(transactions)
      text "Number of fundings: #{transactions_decorator.incoming_count}. Amount of fundings: #{ '%.2f' % transactions_decorator.incoming_sum} #{@project.currency}."
      text "Number of charges: #{transactions_decorator.outgoing_count}. Amount of charges: #{ '%.2f' % transactions_decorator.outgoing_sum} #{@project.currency}."
      text "Current account balance: #{transactions_decorator.current_balance} #{@project.currency}.", style: :bold
    end

    text "The table below shows the current status of the reimbursement process for #{@project.clinical_protocol_code} study."
  end

  def generate_site_text(site)
    site_text = ""

    # active subjects
    site_subjects = @site_statistics[site.id][:cu_count]
    if site_subjects.zero?
      site_text += "There #{site_subjects < 2 ? 'is' : 'are'} #{site_subjects} active #{'subject'.pluralize(site_subjects)}' #{'account'.pluralize(site_subjects)} at the site. "
    else
      site_text += "There are no subjects' accounts added to the site. "
    end

    # transfers waiting
    transfers_awaiting = @site_statistics[site.id][:visits_transfer_waiting]
    if transfers_awaiting > 1
      site_text += "There #{transfers_awaiting < 2 ? 'is' : 'are'} #{transfers_awaiting} #{'payment'.pluralize(transfers_awaiting)} waiting for approval. "
    end

    # transfers processed
    transfers_processed = @site_statistics[site.id][:visits_transfer_in_progress]
    if transfers_processed > 1
      site_text += "#{transfers_processed} #{'payment'.pluralize(transfers_processed).downcase} #{transfers_processed < 2 ? 'is' : 'are'} being processed. "
    end

    transfers_size = @site_statistics[site.id][:visits_transfer_paid]
    if transfers_size > 0
      transfers_amount = @site_statistics[site.id][:visits_transfer_paid_sum]
      site_text += "Subjects have received payments for #{transfers_size} #{'visit'.pluralize(transfers_size)} (#{number_to_currency(transfers_amount , unit: @project.currency)} in total). "
    end

    actions = site.user_action_requirements
    actions_size = actions.size
    not_resolved_size = actions.not_resolved.size
    if site.user_action_requirements.size > 0
      site_text += "There #{actions_size < 2 ? 'is' : 'are'} #{actions_size} action #{'item'.pluralize(actions_size)} assigned to subjects"
      if not_resolved_size > 0
        site_text += ", #{not_resolved_size} of them #{not_resolved_size < 2 ? 'is' : 'are'} not resolved."
      else
        site_text += "."
      end
    end

    text site_text
  end

  def data_for_clinical_site_subjects_table(site)
    data = []

    # Headers
    data << [
      "Subject",
      "Visits to be<br>reimbursed",
      "Awaiting<br>approval",
      "Payments<br>in process",
      "Payments<br>completed",
      "Action<br>items",
      "Recent<br>activity"
    ]

    visits_to_be_reimbursed_total = 0
    visits_to_be_reimbursed_sum_total = 0
    visits_transfer_waiting_total = 0
    visits_transfer_waiting_sum_total = 0
    visits_transfer_in_progress_total = 0
    visits_transfer_in_progress_sum_total = 0
    visits_transfer_paid_total = 0
    visits_transfer_paid_sum_total = 0
    action_items_sum_total = 0

    site.clinical_users.includes(:user_action_requirements).each do |cu|
      visits_to_be_reimbursed = cu.visits.includes(:visit_payment_categorizations).to_be_reimbursed
      visits_to_be_reimbursed_total += visits_to_be_reimbursed.size
      visits_to_be_reimbursed_sum = visits_to_be_reimbursed.sum {|x| x.amount.to_f }
      visits_to_be_reimbursed_sum_total += visits_to_be_reimbursed_sum

      visits_transfer_waiting = cu.visits.includes(:visit_payment_categorizations).transfer_waiting
      visits_transfer_waiting_total += visits_transfer_waiting.size
      visits_transfer_waiting_sum = visits_transfer_waiting.sum {|x| x.amount.to_f }
      visits_transfer_waiting_sum_total += visits_transfer_waiting_sum

      visits_transfer_in_progress = cu.visits.includes(:visit_payment_categorizations).transfer_in_progress
      visits_transfer_in_progress_total += visits_transfer_in_progress.size
      visits_transfer_in_progress_sum = visits_transfer_in_progress.sum {|x| x.amount.to_f }
      visits_transfer_in_progress_sum_total += visits_transfer_in_progress_sum

      visits_transfer_paid = cu.visits.includes(:visit_payment_categorizations).transfer_paid_or_grouped
      visits_transfer_paid_total += visits_transfer_paid.size
      visits_transfer_paid_sum = visits_transfer_paid.sum(:amount)
      visits_transfer_paid_sum_total += visits_transfer_paid_sum

      action_items_sum = cu.user_action_requirements.size
      action_items_sum_total += action_items_sum

      # account_second_row = if cu.transfer_to_bank_account?
      #                        "Linked to #{cu.formatted_account_number}"
      #                      elsif cu.transfer_to_post?
      #                        "Linked to patient’s home address."
      #                      else
      #                         "Subject's internal custody account"
      #                      end

      data << [
        "#{cu.patient_code}<br>#{cu.closed ? "Closed account" : "Active account"}",
        "#{visits_to_be_reimbursed.size}<br>#{number_to_currency(visits_to_be_reimbursed_sum , unit: '')} #{@project.currency}",
        "#{visits_transfer_waiting.size}<br>#{number_to_currency(visits_transfer_waiting_sum , unit: '')} #{@project.currency}",
        "#{visits_transfer_in_progress.size}<br>#{number_to_currency(visits_transfer_in_progress_sum , unit: '')} #{@project.currency}",
        "#{visits_transfer_paid.size}<br>#{number_to_currency(visits_transfer_paid_sum , unit: '')} #{@project.currency}",
        action_items_sum,
        "#{cu.last_activity_date ? time_ago_in_words(cu.last_activity_date).gsub("about ", '') + '<br> ago' : 'None'}"
      ]
    end

    # Add summary row
    data << [
      "All subjects",
      "#{visits_to_be_reimbursed_total}<br>#{number_to_currency(visits_to_be_reimbursed_sum_total , unit: '')} #{@project.currency}",
      "#{visits_transfer_waiting_total}<br>#{number_to_currency(visits_transfer_waiting_sum_total , unit: '')} #{@project.currency}",
      "#{visits_transfer_in_progress_total}<br>#{number_to_currency(visits_transfer_in_progress_sum_total , unit: '')} #{@project.currency}",
      "#{visits_transfer_paid_total}<br>#{number_to_currency(visits_transfer_paid_sum_total , unit: '')} #{@project.currency}",
      action_items_sum_total,
      ""
    ]

    data
  end

  def make_header(anchor, section_name)
    add_dest(anchor, dest_xyz(0, y, nil, page))
    font "Source_Sans_Pro", size: 18
    text section_name
    move_down(20)
  end

  def data_for_subject_visits_table(subject_visits)
    data = []

    # Headers
    data << [
      "Visit date",
      "Visit name",
      "Status",
      "Amount",
      "Recent<br>activity"
    ]

    total_amount = 0
    subject_visits.each do |visit|
      total_amount += visit.amount if visit.amount

      data << [
        "#{visit.visit_date.present? ? visit.visit_date.strftime("%d-%m-%Y") : 'N/A'}",
        visit.name_formatted,
        visit.actual_state,
        "#{number_to_currency(visit.amount, unit: '')} #{@project.currency}",
        "#{visit.activities.any? ? time_ago_in_words(visit.activities.last.created_at).gsub("about ", '') + ' ago' : 'None'}"
      ]
    end

    # Add summary row
    data << [
      "Total",
      "",
      "",
      "#{number_to_currency(total_amount, unit: '')} #{@project.currency}",
      ""
    ]


    data
  end

  def data_for_subject_actions_required_table(actions)
    data = [ ['Date', 'Issue', 'Status'] ]
    actions.each do |a|
      data << [
        a.created_at.strftime("%d-%m-%Y"),
        a.reason,
        a.state
      ]
    end
    data
  end

  def data_for_subject_payments_table(payments)
    data = [ ['Date', 'Amount', 'Bank account', 'Visit name', 'Visit date'] ]

    payments.each do |transfer|
      data << [
        transfer.created_at.strftime("%d-%m-%Y"),
        "#{number_to_currency(transfer.amount, unit: '')} #{@project.currency}",
        '****',
        transfer.visit.name,
        transfer.visit.visit_date.strftime("%d-%m-%Y")
      ]
    end

    data
  end

  def load_site_statistics
    @site_statistics = {}
    @all_sites_statistics = {}

    # Summary row variables
    @all_sites_statistics = {
      cu_count_total: 0,
      visits_planned_total: 0,
      visits_transfer_waiting_total: 0,
      visits_transfer_in_progress_total: 0,
      visits_transfer_paid_total: 0,
      visits_planned_sum_total: 0,
      visits_transfer_waiting_sum_total: 0,
      visits_transfer_in_progress_sum_total: 0,
      visits_transfer_paid_sum_total: 0,
      cu_actions_count_total: 0
    }

    @clinical_centers.each do |cc|
      @site_statistics[cc.id] = {}

      @site_statistics[cc.id][:cu_count] = cc.clinical_users.includes(:user_action_requirements).size
      @all_sites_statistics[:cu_count_total] += @site_statistics[cc.id][:cu_count]
      @site_statistics[cc.id][:cu_actions_count] = cc.clinical_users.includes(:user_action_requirements).map(&:user_action_requirements).flatten.size
      @all_sites_statistics[:cu_actions_count_total] += @site_statistics[cc.id][:cu_actions_count]

      @site_statistics[cc.id][:visits_planned] = cc.visits.includes(:visit_payment_categorizations).to_be_reimbursed.size
      @all_sites_statistics[:visits_planned_total] += @site_statistics[cc.id][:visits_planned]
      @site_statistics[cc.id][:visits_planned_sum] = cc.visits.includes(:visit_payment_categorizations).to_be_reimbursed.sum {|x| x.amount.to_f }
      @all_sites_statistics[:visits_planned_sum_total] += @site_statistics[cc.id][:visits_planned_sum]

      @site_statistics[cc.id][:visits_transfer_waiting] = cc.visits.includes(:visit_payment_categorizations).transfer_waiting.size
      @all_sites_statistics[:visits_transfer_waiting_total] += @site_statistics[cc.id][:visits_transfer_waiting]
      @site_statistics[cc.id][:visits_transfer_waiting_sum] = cc.visits.includes(:visit_payment_categorizations).transfer_waiting.sum {|x| x.amount.to_f }
      @all_sites_statistics[:visits_transfer_waiting_sum_total] += @site_statistics[cc.id][:visits_transfer_waiting_sum]

      @site_statistics[cc.id][:visits_transfer_in_progress] = cc.visits.includes(:visit_payment_categorizations).transfer_in_progress.size
      @all_sites_statistics[:visits_transfer_in_progress_total] += @site_statistics[cc.id][:visits_transfer_in_progress]
      @site_statistics[cc.id][:visits_transfer_in_progress_sum] = cc.visits.includes(:visit_payment_categorizations).transfer_in_progress.sum {|x| x.amount.to_f }
      @all_sites_statistics[:visits_transfer_in_progress_sum_total] += @site_statistics[cc.id][:visits_transfer_in_progress_sum]

      @site_statistics[cc.id][:visits_transfer_paid] = cc.visits.includes(:visit_payment_categorizations).transfer_paid_or_grouped.size
      @all_sites_statistics[:visits_transfer_paid_total] += @site_statistics[cc.id][:visits_transfer_paid]
      @site_statistics[cc.id][:visits_transfer_paid_sum] = cc.visits.includes(:visit_payment_categorizations).transfer_paid_or_grouped.sum(:amount)
      @all_sites_statistics[:visits_transfer_paid_sum_total] += @site_statistics[cc.id][:visits_transfer_paid_sum]
    end
  end

  def header
    text "Let Me Pay Sp. z o.o.", size: 10, align: :right, style: :bold
    text 'Iłżecka 26, 02-135 Warsaw', size: 10, align: :right

    bounding_box([0, cursor + 30], width: 400) do
      image "#{Rails.root}/public/pc-logo-dark.png", width: 170
    end
  end

  def footer
    options = { at: [bounds.right - 150, -10], size: 12,  width: 150, align: :right, start_count_at: 1 }
    number_pages "Page <page> / <total>", options
  end

  def red_notification(notification_text)
    fill_color 'e02361'
    font "Source_Sans_Pro", size: 10
    text notification_text
    fill_color '000000'
    font "Source_Sans_Pro", size: 14
  end

  def bank_nr_with_asterisks(bank_nr)
    bank_nr[0..3] + '****' + bank_nr[-4..-1]
  end

  def recent_activity_text(acion)
    action = not_resolved_actions.order_by_id.try(:first)
    if action
      created_days_ago = (Date.today - action.created_at.to_date).to_i
      if created_days_ago > 0
        return "#{created_days_ago} #{'day'.pluralize(created_days_ago)} ago"
      else
        return "today"
      end
    end
  end
end
