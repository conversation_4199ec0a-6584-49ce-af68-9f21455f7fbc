module Invoices::ProjectStartupFees
  def project_startup_fees_page
    start_new_page
    attachment_title(attachment_symbol: 'project_startup_fees', title: I18n.t('invoice_pdf.summary_page.project_startup_fees'))
    move_down 10

    table project_startup_fees_rows, header: true, width: bounds.width do |table|
      no_border_pdf_table(table)
      four_column_fee_table_style(table)
    end

    move_down 30

    text I18n.t('invoice_pdf.one_time_site_fee', dates_from_to: dates_from_to, amount: formatted_number(report.project_startup_fees_total), fee_currency: fee_currency, protocol: report.project.clinical_protocol_code)

    move_down 10

    text report.project_startup_fees_total_summary_row, style: :bold
    foreign_currency_fee_section(:project_startup_fees_total)
  end

  def project_startup_fees_rows
    total = 0

    [report.project_startup_fees_header] +
      report.project_startup_fees.map do |fee|
        total += fee.amount
        report.project_startup_row(fee, total)
      end
  end

  def project_startup_fees_first_page_row
    fees_first_page_row(
      base_row: base_project_startup_fees_first_page_row,
      total_with_vat: report.project_startup_fees_total_with_vat,
      total_vat: report.project_startup_fees_total_vat
    )
  end
end
