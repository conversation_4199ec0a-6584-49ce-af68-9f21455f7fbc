class ProjectDebits::PdfContent < SimpleDelegator
  attr_accessor :pd, :pdf_number, :amounts_sum, :all_sum,
                :pdf_number, :attachment_strings, :summary_header, :attachments,
                :each_researcher_transfers, :pdf_locale, :pages_att_hash, :skip_first_page

  include SharedPdf

  def initialize(prawn, params = {})
    super(prawn)

    @pages_att_hash = {}
    params.each do |key, value|
      instance_variable_set("@#{key}", value)
    end
  end

  def render
    first_page unless @skip_first_page
    attachment_pages
    set_note_numbers_in_footer
    footer_right_page_nr
  end

  def first_page
    header
    pages_att_hash[page_number] = 0
    text I18n.t('project_debit_pdf_quintiles.transfers_summary_nr', nr: pdf_number), style: :bold, size: 14
    text I18n.t('project_debit_pdf_quintiles.order_within', from: I18n.l(pd.start_date, format: :only_date_slash), to: I18n.l(pd.completion_date, format: :only_date_slash)), style: :bold
    move_down(10)
    text I18n.t('pdf_shared.create_localization')
    text I18n.t('pdf_shared.created_at', date: I18n.l(pd.completion_date, format: :only_date_slash))

    move_down(10)
    move_down(10)
    text 'Let Me Pay Sp. z o.o.'
    text 'Iłżecka 26, 02-135 Warszawa'
    text 'NIP: 522-29-93-346'
    move_down(20)
    bounding_box([320, 600], width: 200) do
      move_down(85)
      text pd.project.company_name.to_s, style: :bold
      text pd.project.company_street.to_s, style: :bold
      text "#{pd.project.company_zip_code} #{pd.project.company_city}", style: :bold
      text "NIP: #{ProjectDebit.format_nip_number(pd.project.company_nip)}", style: :bold
      move_down(10)
    end
    move_down(20)
    text "#{I18n.t('pdf_shared.project_code')}: <b>#{pd.project.clinical_protocol_code}</b>", inline_format: true

    if pd.project.clinical_trial_title.present?
      text "#{I18n.t('project_debit_pdf_quintiles.research_title')} <b>#{pd.project.clinical_trial_title}</b>", inline_format: true
    end

    text "#{I18n.t('pdf_shared.sponsor')}: <b>#{pd.project.name}</b>", inline_format: true

    move_down(20)

    if amounts_sum.size > 1
      table amounts_sum, header: true, width: 530 do |table|
        table.column(0).style align: :center
        table.column(1).style align: :center
        table.column(2).style align: :right
        table.column(3).style align: :right
        table.column(4).style align: :center
        table.column(5).style align: :center

        table.row(0).style align: :center, valign: :center, border_widths: [0, 0, 1, 0]
        table.row(0).font_style = :bold
        table.row(1..99_999).style height: 21, border_width: 0
      end
      move_down(20)
      text I18n.t('pdf_shared.amount_to_pay_with_currency', amount: all_sum, currency: pd.currency), inline_format: true
    else
      text I18n.t('project_debit_pdf_quintiles.no_transfers')
    end
  end

  def attachment_pages
    att_num = 0

    attachments.each.with_index do |k, i|
      start_new_page unless @skip_first_page && i == 0

      attachment_str = "#{format('%03d', att_num += 1)}-#{k[0].clinical_center_code}"
      attachment_strings << "#{attachment_str}/#{pdf_number}"
      text "#{I18n.t('pdf_shared.debit_nr')} #{pdf_number}", style: :bold, size: 14, align: :center

      pds = ProjectDebitSignature.find_quintiles_sig(pd.id, k[0].id, k[1].id)
      pages_att_hash[page_number] = att_num

      move_down(33)
      text "#{I18n.t('pdf_shared.project_code')}: <b>#{pd.project.clinical_protocol_code}</b>", inline_format: true

      if pd.project.clinical_trial_title.present?
        text "#{I18n.t('project_debit_pdf_quintiles.research_title')} <b>#{pd.project.clinical_trial_title}</b>", inline_format: true
      end

      text "#{I18n.t('pdf_shared.sponsor')}: <b>#{pd.project.name}</b>", inline_format: true
      move_down(15)

      accepted_by = pd.accepting_researcher
      if k[1].qid.present?
        text "#{I18n.t('project_debit_pdf_quintiles.person_ordering_transfers')}: <b>#{accepted_by.try(:full_name)} (#{pd.try(:pdf_researcher_id_name)}: #{accepted_by.try(:qid)})</b>", inline_format: true
      else
        text "#{I18n.t('project_debit_pdf_quintiles.person_ordering_transfers')}: <b>#{accepted_by.try(:full_name)}</b>", inline_format: true
      end

      text "#{I18n.t('project_debit_pdf_quintiles.center')}: <b>#{k[0].clinical_center_code} (#{k[0].city})</b>", inline_format: true
      move_down(15)
      text I18n.t('project_debit_pdf_quintiles.transfers_summary_in_period', from: I18n.l(pd.start_date, format: :only_date_slash), to: I18n.l(pd.completion_date, format: :only_date_slash)), style: :bold
      move_down(10)

      tmp_sum = 0

      transfer_to_cc = false
      project_debits_table = each_researcher_transfers[k[0].id][k[1].id].map do |x|
        transfer_to_cc = true if x[:visit_paid_to_cc] == true
        transfer_amount = x[:transfer_amount].try(:to_d)

        if x[:transfered_to] == 'clinical_center'
          transfer_to_cc = true
          final_amount = "#{x[:transfer_amount]} (*)"
        else
          final_amount = x[:transfer_amount]
        end

        [x[:created_at], x[:patient_code], x[:visit_date], x[:visit_name],
         x[:visit_payment_categorizations].to_s,
         final_amount,
         format('%.2f', tmp_sum += x[:transfer_amount].to_f).to_s]
      end
      project_debits_table.reverse!
      project_debits_table << [I18n.t('project_debit_pdf_quintiles.order_date'), I18n.t('project_debit_pdf_quintiles.patient_nr'),
                               I18n.t('project_debit_pdf_quintiles.visit_date'), I18n.t('project_debit_pdf_quintiles.visit_name'),
                               I18n.t('project_debit_pdf_quintiles.expenses_by_type'), I18n.t('project_debit_pdf_quintiles.visit_amout'),
                               I18n.t('project_debit_pdf_quintiles.visit_balance')]
      project_debits_table.reverse!

      table project_debits_table, header: true, width: 530 do |table|
        table.column(0).style align: :center, size: 10, width: 65
        table.column(1).style align: :center, size: 10
        table.column(2).style align: :center, size: 10, width: 65
        table.column(3).style align: :center, size: 10, width: 110
        table.column(4).style align: :center, size: 10, inline_format: true, width: 80
        table.column(5).style align: :center, size: 10, inline_format: true
        table.column(6).style align: :center, size: 10, width: 65

        table.row(0).style align: :center, valign: :center, border_widths: [0, 0, 1, 0]
        table.row(0).font_style = :bold
        table.row(1..99_999).style border_width: 0
      end
      move_down(10)
      text GenerateVisitLegend.call(project: pd.project, locale: I18n.locale), size: 10, style: :italic

      if transfer_to_cc
        move_down(5)
        text I18n.t('project_debit_pdf_quintiles.transfer_to_cc', site_acc_nr: pd.clinical_center.try(:formatted_account_number) || ''), size: 10, style: :italic
      end
      move_down(25)
      sum_amount = project_debits_table[project_debits_table.size - 1][6]

      if pd.note_currency_exchange
        text I18n.t('project_debit_pdf_quintiles.total_amount_foreign', amount: pd.foreign_amount_for_summary, pln_amount: sum_amount, currency: pd.note_foreign_currency), size: 14, style: :bold, inline_format: true
        text I18n.t('project_debit_pdf_quintiles.currency_exchange', rate: pd.note_currency_exchange.currency_rate_used.round(4), base_currency: pd.note_currency_exchange.base_currency, target_currency: pd.note_currency_exchange.target_currency), size: 12, inline_format: true
      else
        text I18n.t('project_debit_pdf_quintiles.total_amount', amount: sum_amount, currency: pd.currency), size: 14, style: :bold, inline_format: true
      end
      move_down(20)
      text I18n.t('project_debit_pdf_quintiles.confirmation'), style: :bold, inline_format: true
      move_down(45)

      pds = ProjectDebitSignature.find_quintiles_sig(pd.id, k[0].id, k[1].id)
      signed_by_researcher = pds.try(:signed_by_researcher) || k[1]

      unless pd.zero_balance?
        if pds&.signed?
          fill_color '646161'
          text I18n.t('project_debit_pdf_quintiles.doc_signed_on', date: I18n.l(pds.signature_date&.utc, format: :date_dash_at)), style: :italic
          signed_by_text = pds.auto_signed ? I18n.t('pdf_shared.on_behalf_of') : I18n.t('pdf_shared.by')
          if signed_by_researcher.qid.present?
            text "#{signed_by_text}: #{signed_by_researcher.full_name} (#{pds.pdf_researcher_id_name}: #{signed_by_researcher.qid}, #{signed_by_researcher.email})", style: :italic
          else
            text "#{signed_by_text}: #{signed_by_researcher.full_name} (#{signed_by_researcher.email})", style: :italic
          end
          text "Purchase Order: #{pds.po.present? ? pds.po : 'N/A'}", style: :italic
          text "#{I18n.t('project_debit_pdf_quintiles.signature')}: #{pds.public_hash.try(:upcase)}", style: :italic
          fill_color '000000'
        else
          text " \n \n \n"
        end
      end

      move_down(20)

      start_new_page if y < 130

      text '..............................................'
      text signed_by_researcher.full_name.to_s
      move_down(20)
      text "Email: #{signed_by_researcher.email}", size: 10
      if signed_by_researcher.phone_number.present?
        text "#{I18n.t('pdf_shared.phone')}: #{signed_by_researcher.phone_number}", size: 10
      end
    end
    end

  def header
    text 'Let Me Pay Sp. z o.o.', size: 10, align: :right, style: :bold
    text 'Iłżecka 26, 02-135 Warsaw', size: 10, align: :right

    bounding_box([0, cursor + 30], width: 400) do
      image logopath, width: 170
    end

    move_down(30)
  end

  def set_note_numbers_in_footer(page_index: 1)
    page_count.times do |i|
      page_nr = i + page_index
      unless page_nr > page_count
        go_to_page(page_nr)
        bottom_left_note_number
      end
    end
  end

  def bottom_left_note_number
    left_options = { at: [0, 0], size: 10, width: 300, align: :left, height: 50 }
    text_box pd.full_note_number, left_options
  end

  def logopath
    "#{Rails.root}/public/pc-logo-dark.png"
  end
  end
