class ProjectDebits::StandardPdf < Prawn::Document
  include SharedPdf

  attr_accessor :pd, :pdf_number, :amounts_sum, :all_sum,
                :pdf_number, :attachment_strings, :summary_header, :attachments,
                :each_researcher_transfers, :pdf_locale, :pages_att_hash, :skip_first_page

  def initialize(params = {})
    super({ page_size: "A4" })
    font_and_color_setup

    @content = ProjectDebits::PdfContent.new(
      self,
      params
    )
    @content.render
  end

  def pages_att_hash
    @content.pages_att_hash
  end
end
