class BulkInvoicePdf < Prawn::Document
  include SharedPdf
  include Invoices::SharedCompanyInfo
  include Invoices::PdfFooter
  include ReportsHelper

  attr_reader :cro, :invoice_number, :report, :created_for_from, :created_for_to, :locale, :projects, :invoice

  def initialize(params: {}, prawn_options: {})
    super prawn_options
    @invoice = params[:invoice]
    @cro = invoice.cro
    @projects = params[:projects]
    @invoice_number = invoice.number
    @report = params[:report]
    @created_for_from = invoice.created_for_from
    @created_for_to = invoice.created_for_to
    @comment = invoice.get_comment_for_pdf
    @locale = params[:locale] || cro.invoice_locale || 'pl'

    font_and_color_setup

    page_size = 'A4'
    I18n.locale = locale
    content
  end

  def content
    title_page

    projects.each do |p|
      attachment_for_project(p)
    end

    footer_right_page_nr
  end

  def attachment_for_project(project)
    start_new_page
    ProjectInvoicePdfContent.new(self,
                                 project: project,
                                 invoice: invoice,
                                 invoice_number: report.attachment_nr_for_project(project, invoice_number: invoice_number),
                                 locale: locale,
                                 report: ProjectFeeReport.new(project_id: project.id, start_date: created_for_from.beginning_of_day, end_date: created_for_to.end_of_day)).render
  end

  def title_page
    lmp_pdf_header
    move_down(10)
    text I18n.t('bulk_invoice_pdf.invoice_nr', invoice_number: invoice_number), style: :bold, size: 14
    move_down(10)
    text I18n.t('notes.normal.created_at', date: I18n.l(created_for_to, format: :only_date_slash))
    move_down(29)
    Shared::LmpAndCroInfoSection.new(self, cro: cro).render

    move_down(20)
    if @locale == 'pl'
      text "Przedmiotem usługi jest prowadzenie rachunków z wykorzystaniem platformy płatniczej PayClinical.com oraz rozliczanie kosztów podróży do ośrodka i innych kosztów poniesionych przez pacjentów w związku z ich uczestnictwem w badaniu klinicznym zgodnie ze specyfikacją załączoną poniżej."
      move_down 10
    end

    first_page_table

    if @comment
      move_down(10)
      text @comment
    end
    move_down(15)
    text I18n.t('bulk_invoice_pdf.amount_to_pay', amount: formatted_number(report.total_fees_with_vat), currency: cro.invoice_currency), inline_format: true
    reverse_charge_section
    move_down(10)

    text I18n.t('project_debit_summary_pdf.payment_title', nr: invoice_number), inline_format: true
    text I18n.t('project_debit_summary_pdf.payment_till', date: I18n.l(created_for_to + invoice.company_invoice_payment_end_days.days, format: :only_date_slash)), inline_format: true

    move_down(13)

    text I18n.t('project_debit_summary_pdf.transfer_to') + ' <b>' + LmpSetting.invoice_acc_nr(currency: cro.invoice_currency) + '</b>', size: 12, inline_format: true
    text 'Swift: <b>ALBPPLPW</b>', size: 12, inline_format: true

    move_down(20)

    text I18n.t('pdf_shared.contact'), style: :italic, :size => 12,  :align => :left
  end

  def first_page_table
    nr_of_last_bold_rows = report.all_fees.for_transfers.any? ? 3 : 2

    table first_page_table_rows, header: true, width: bounds.width do |table|
      table.column(0..1).style align: :left, valign: :center
      table.column(2..7).style align: :right, width: 60, valign: :center
      table.column(0).style width: 130, align: :center
      no_border_pdf_table(table)
      table.row(0).column(3..7).style align: :right, width: 70, valign: :center
      table.row((0 - nr_of_last_bold_rows)..-1).font_style = :bold
      table.column(2).style align: :right
      table.row(-1).column(-4..-1).style border_widths: [1, 0, 0, 0]
    end
  end

  def first_page_table_rows
    rows = [
      first_page_table_header
    ]

    total_net = 0
    total_vat = 0
    no_vat_amount = 0

    report.projects_with_fees.each.with_index do |p, _i|
      net_amount = report.total_vattable_fees_for_project_converted_to_pln(p).round(2)
      vat_amount = report.total_vat_for_project(p).round(2)
      amount = net_amount + vat_amount

      total_net += net_amount
      total_vat += vat_amount

      row = if foreign_company?
        [
          p.clinical_protocol_code,
          report.attachment_nr_for_project(p, invoice_number: invoice_number),
          formatted_number(net_amount),
          cro_vat_perc,
          formatted_number(amount)
        ]
      else
        [
          p.clinical_protocol_code,
          report.attachment_nr_for_project(p, invoice_number: invoice_number),
          formatted_number(net_amount),
          cro_vat_perc,
          formatted_number(vat_amount),
          formatted_number(amount)
        ]
      end

      rows << row

      if report.transfer_fees_for_project(p).any?
        amount = report.converted_total_transfer_fees_for_project(p)
        no_vat_amount += amount

        row = if foreign_company?
          [
            p.clinical_protocol_code,
            I18n.t("bulk_invoice_pdf.first_page_table.transfer_fees.title"),
            formatted_number(amount),
            'NP',
            formatted_number(amount)
          ]
        else
          [
            p.clinical_protocol_code,
            I18n.t("bulk_invoice_pdf.first_page_table.transfer_fees.title"),
            formatted_number(amount),
            'zw.',
            '0.00',
            formatted_number(amount)
          ]
        end

        rows << row
      end
    end

    total = total_net.round(2) + total_vat.round(2)

    row = if foreign_company?
      [
        I18n.t('bulk_invoice_pdf.first_page_table.total'),
        '',
        formatted_number(total_net),
        cro_vat_perc,
        formatted_number(total)
      ]
    else
      [
        I18n.t('bulk_invoice_pdf.first_page_table.total'),
        '',
        formatted_number(total_net),
        cro_vat_perc,
        formatted_number(total_vat),
        formatted_number(total)
      ]
    end
    rows << row

    if no_vat_amount > 0
      row = if foreign_company?
        [
          '',
          '',
          formatted_number(no_vat_amount),
          cro.foreign? ? 'NP' : 'zw.',
          formatted_number(no_vat_amount)
        ]
      else
        [
          '',
          '',
          formatted_number(no_vat_amount),
          cro.foreign? ? 'NP' : 'zw.',
          formatted_number(0),
          formatted_number(no_vat_amount)
        ]
      end
      rows << row
    end

    total_net += no_vat_amount
    total += no_vat_amount

    row = if foreign_company?
      [
        '',
        '',
        formatted_number(total_net),
        '',
        formatted_number(total)
      ]
    else
      [
        '',
        '',
        formatted_number(total_net),
        '',
        formatted_number(total_vat),
        formatted_number(total)
      ]
    end
    rows << row

    rows
  end

  def cro_vat_perc
    cro.foreign? ? 'NP' : '23%'
  end

  def foreign_company?
    cro.foreign?
  end

  def first_page_table_header
    if foreign_company?
      [
        I18n.t('bulk_invoice_pdf.first_page_table.protocol'),
        I18n.t('bulk_invoice_pdf.first_page_table.summary'),
        I18n.t('bulk_invoice_pdf.first_page_table.net_amount'),
        I18n.t('bulk_invoice_pdf.first_page_table.vat_rate'),
        I18n.t('bulk_invoice_pdf.first_page_table.gross_amount')
      ]
    else
      [
        I18n.t('bulk_invoice_pdf.first_page_table.protocol'),
        I18n.t('bulk_invoice_pdf.first_page_table.summary'),
        I18n.t('bulk_invoice_pdf.first_page_table.net_amount'),
        I18n.t('bulk_invoice_pdf.first_page_table.vat_rate'),
        I18n.t('bulk_invoice_pdf.first_page_table.vat_amount'),
        I18n.t('bulk_invoice_pdf.first_page_table.gross_amount')
      ]
    end
  end
end
