class VisitReminderForm < BaseFormObject
  attr_accessor :visit, :reminderer

  validate :cant_be_sent_sooner_than_every_seven_days
  validate :may_send_remidner

  private

  def persist!
    VisitReminder.transaction do
      VisitReminder.create!(visit_id: visit.id, reminderer_id: reminderer.id, reminderer_type: reminderer.class.name)
      visit.clinical_user_account_activities.create!(researcher_id: reminderer.id, clinical_user_id: visit.clinical_user_id, activity_type: 'send_visit_reminder') if reminderer.is_a?(Researcher)
      ResearcherMailer.with(visit: visit, reminderer: reminderer, send_to_researcher: send_to, cc_to: cc_to).send_visit_reminder.deliver
    end
  end

  def send_to_researchers
    ResearchersInProject.run(project_id: visit.project.id, project_role: ['CRA', 'CRA+'])
  end

  def may_send_remidner
    unless visit.may_send_reminder?
      msg = if reminderer.is_a?(Researcher)
        'You are not authorized to use this function.'
      else
        'Nie można wys<PERSON> powiadomienia.'
      end
      errors.add(:base, msg)
    end
  end

  def cant_be_sent_sooner_than_every_seven_days
    blocking_reminder = visit.visit_reminders.where('created_at > ?', 7.days.ago).last
    if blocking_reminder.present?
      msg = if reminderer.is_a?(Researcher)
        "The previous reminder about #{ visit.name } of patient #{ visit.clinical_user.try(:patient_code) } has been sent on #{ I18n.l(blocking_reminder.created_at, format: :date_dash_at) }. The next reminder can be send after 7 days."
      else
        "Poprzednie przypomnienie zostało wysłane dnia #{ I18n.l(blocking_reminder.created_at, format: :date_dash_at) }. Kolejne przypomnienie może zostać wysłane po upływie 7 dni."
      end
      errors.add(:base, msg)
    end
  end

  def send_to
    ActionItems::Targets::Cras::GetTo.call(action_item: ActionItem.new(resource: visit)) || reminderer
  end

  def cc_to
    ActionItems::Targets::Cras::GetCc.call(action_item: ActionItem.new(resource: visit)).pluck(:email)
  end

  def clinical_center
    visit.clinical_center
  end
end
