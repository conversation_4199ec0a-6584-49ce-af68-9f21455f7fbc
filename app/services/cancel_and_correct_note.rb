class CancelAndCorrectNote
  attr_reader :errors

  def initialize(pds:, send_email: false)
    @pds = pds
    @errors = []
    @send_email = send_email
  end

  def call
    begin
      ProjectDebitSummary.transaction do
        generate_note_correction
        cancel_note

        if @send_email
          @note_correction.update!(state: 'sent')
          NoteCorrectionMailer.with(note: @note_correction).send_to_cro.deliver
        end
      end
      true
    rescue => e
      @errors << e.to_s
      return false
    end
  end

  def generate_note_correction
    @note_correction = @pds.note_corrections.create!(all_transfers: true, clinical_transfer_ids: @pds.clinical_transfers.pluck(:id).uniq)
  end

  def cancel_note
    @pds.cancel
  end
end