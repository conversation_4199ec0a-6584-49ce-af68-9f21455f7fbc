class ResendInvitationsToResearchers
  def initialize(params = {})
    @researchers = params.fetch :researchers, Researcher.unconfirmed.where(created_at: [1.week.ago.all_day, 2.week.ago.all_day])
  end

  def call
    @researchers.each do |r|
      begin
        r.send_new_password_instructions
      rescue Exception => e
        puts e
        ExceptionNotifier.notify_exception(e, data: { message: e.to_s })
      end
    end
  rescue => e
    puts e
    ExceptionNotifier.notify_exception(e, data: { message: e.to_s })
  end
end
