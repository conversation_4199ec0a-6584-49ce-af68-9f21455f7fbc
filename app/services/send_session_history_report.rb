class SendSessionHistoryReport
  def initialize(params = {})
    @email = params.fetch(:email, ['<EMAIL>'])
    @date_range = params.fetch(:date_range, (Time.now.beginning_of_day..Time.now.end_of_day))
    @type = params.fetch(:type, [:researcher, :clinical_user])
  end

  def call
    Array(@type).each { |t| send("send_#{t}_logins") }
  rescue => e
    p 'blad przy wysylaniu raportow logowan' + " Error: #{e.to_s}"
    ExceptionNotifier.notify_exception(e, data: { message: 'blad przy wysylaniu raportow logowan' })
  end

  private

  def send_researcher_logins
    # ResearcherMailer.with(researcher_logins: researcher_logins, email: @email, date_range: @date_range, unregistered_user_email_logins: unregistered_user_email_logins).send_session_history_report.deliver
  end

  def send_clinical_user_logins
    # ClinicalMailer.clinical_users_session_history_report(user_logins: user_logins, email: @email, date_range: @date_range).deliver
  end

  def researcher_logins
    SessionHistory.researcher_histories.where(created_at: @date_range).order('created_at desc')
  end

  def unregistered_user_email_logins
    SessionHistory.where(created_at: @date_range, researcher_id: nil, clinical_user_id: nil).order('created_at desc')
  end

  def user_logins
    SessionHistory.clinical_user_histories.where(created_at: @date_range).order('created_at desc')
  end
end
