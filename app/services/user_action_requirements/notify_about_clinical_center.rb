class UserActionRequirements::NotifyAboutClinicalCenter < ApplicationService
  def initialize(clinical_center:, researcher: nil, send_to_admin: false)
    @clinical_center = clinical_center
    @researcher = researcher || most_active_researcher
    @send_to_admin = send_to_admin
  end

  def call
    unless @researcher
      puts 'No researcher found, skipping'
      return
    end

    actions = actions_for_researcher

    return unless actions.any?

    puts "Sending to researcher #{@researcher.id}"

    UserActionRequirements::NotifyResearcherAboutClinicalCenter.call(
      researcher: @researcher,
      clinical_center: @clinical_center,
      user_action_requirements: actions,
      send_to_admin: @send_to_admin
    )
  end

  private

  def actions_for_researcher
    UserActionRequirements::NotNotifiedOrNotifiedBeforeQuery.run(
      researcher: @researcher,
      user_action_requirements: UserActionRequirement
      .where(
        clinical_user_id: @researcher.clinical_users
                                    .where(clinical_center_id: @clinical_center.id)
      )
                                    .not_resolved
    ).sort_by { |uar| [uar.patient_code || '0', uar.created_at] }
  end

  def most_active_researcher
    ClinicalCenters::GetMostActiveResearcher.call(clinical_center: @clinical_center)
  end
end
