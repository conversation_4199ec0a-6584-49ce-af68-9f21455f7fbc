class CallsAndMeetings::CalendarIcs
  def self.event_ics(call_meeting)
    cal = Icalendar::Calendar.new
    cal.timezone { |t| t.tzid = "Europe/Warsaw" }

    cal.event do |e|
      e.dtstart = call_meeting.date_and_time
      e.dtend   = call_meeting.date_and_time + 30.minutes
      e.summary = "PayClinical: #{call_meeting.title}"
      e.organizer = Icalendar::Values::CalAddress.new("mailto:#{call_meeting.main_receiver}", cn: call_meeting.owner_name)
    end

    cal.to_ical
  end
end
