class GenerateBulkInvoice < ApplicationService
  attr_reader :invoice

  def initialize(cro:, start_date:, end_date:, order_number: nil)
    @cro = cro
    @start_date = start_date
    @end_date = end_date
    @order_number = order_number
  end

  def call
    BulkInvoice.transaction do
      existing_invoice = @cro.bulk_invoices.where(created_for_from: @start_date.to_date, created_for_to: @end_date.to_date)
      PostTransfer.where(invoice_id: existing_invoice).distinct.update_all(invoice_id: nil)
      @old_invoice_id = existing_invoice.maximum(:id)
      existing_invoice.delete_all
      generate_new_invoice
    end
  end

  private

  def generate_new_invoice
    @invoice = BulkInvoice.new(
      contract_research_organization_id: @cro.id,
      created_for_to: @end_date.to_date,
      created_for_from: @start_date.to_date,
      number: number,
      gross_total_amount: gross_total_amount,
      id: @old_invoice_id
    )

    ['en', 'pl'].each do |locale|
      render_pdf(locale)
      @invoice.send("#{ locale }_file_path=", path(locale).sub('public/', ''))
    end

    @invoice.summary_file_path = generate_summary_xls

    @invoice.save!
    post_transfers.update_all(invoice_id: @invoice.id)
    BulkInvoices::GenerateXlsJob.new.perform(bulk_invoice_id: @invoice.id)
  end

  def number
    @number ||= GenerateInvoiceNumber.new(cro: @cro, created_for_to: @end_date, invoice_order_nr: invoice_order_nr, nr_of_projects_in_bulk_invoice: report.projects_with_fees.size).call
  end

  def gross_total_amount
    report.total_fees_with_vat
  end

  def invoice_order_nr
    return @order_number if @order_number.present?

    GetInvoiceOrderNumber.call(created_for_to: @end_date)
  end

  def path(locale)
    "public/system/bulk_invoices/#{file_name(locale)}.pdf".tr(' ', '_')
  end

  def file_name(locale)
    "Faktura_VAT_#{number}_#{@cro.id}_#{ locale }".tr('/', '_')
  end

  def render_pdf(locale)
    pdf(locale).render_file path(locale)
  end

  def pdf(locale)
    BulkInvoicePdf.new(params: { invoice: @invoice, report: report, projects: projects, locale: locale })
  end

  def projects
    report.projects_with_fees
  end

  def report
    @report ||= CroFeeReport.new(cro: @cro, start_date: @start_date, end_date: @end_date, post_transfers: post_transfers, invoice_order_nr: invoice_order_nr)
  end

  def post_transfers
    @cro.post_transfers.uninvoiced_sent_post_transfers_created_before(@end_date)
  end

  def generate_summary_xls
    content = ApplicationController.render(
      'admin/bulk_invoices/summary_report',
      locals: { resource: @invoice },
    )

    file_name = "Faktura_VAT_#{number}_#{@cro.id}_SUMMARY".tr('/', '_')
    file_path = "system/bulk_invoices/#{file_name}.xlsx"
    path = "#{Rails.root}/public/#{file_path}"
    File.open(path, 'w+b') { |f| f.write content }

    file_path
  end

end
