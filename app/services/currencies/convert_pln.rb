class Currencies::ConvertPln < ApplicationService
  def initialize(amount:, base_currency:, target_currency:, date: Time.current, spread: nil)
    @amount = amount
    @base_currency = base_currency
    @target_currency = target_currency
    @date = date
    @spread = spread
  end

  def call
    if @base_currency == 'PLN'
      @amount / rate_pln
    else
      Currencies::Convert.call(
        amount: @amount,
        base_currency: @base_currency,
        target_currency: @target_currency,
        date: @date,
        spread: @spread
      )
    end
  end

  private

  def rate_pln
    pln_rate.rate_with_spread(@spread)
  end

  def pln_rate
    CurrencyRates::FindValidRateForPln.call(
      target_currency: @target_currency,
      base_currency: @base_currency,
      date: @date
    )
  end
end