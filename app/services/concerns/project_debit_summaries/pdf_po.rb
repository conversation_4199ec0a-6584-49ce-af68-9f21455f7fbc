module ProjectDebitSummaries::PdfPo
  def aprover_or_po_label
    if @project_debit_summary.po_required_for_post_paid_note
      I18n.t('project_debit_summary_pdf.postpaid_po')
    else
      I18n.t('project_debit_summary_pdf.aprover')
    end
  end

  def approver_or_po_for_debit(pd)
    if @project_debit_summary.po_required_for_post_paid_note
      pd.signature_po
    else
      pd.accepting_researcher.try(:full_name)
    end
  end
end