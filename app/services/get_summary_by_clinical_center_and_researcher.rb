class GetSummaryByClinicalCenterAndResearcher
  attr_reader :start_date, :end_date, :project

  def initialize(params = {})
    @start_date = params[:start_date].to_date.beginning_of_day
    @end_date = params[:end_date].to_date.end_of_day
    @project = params[:project]
  end

  def call
    tr = project.clinical_transfers.joins(:visit).where('clinical_transfers.created_at >= ? AND clinical_transfers.created_at <= ?', start_date, end_date).outgoing.not_cancelled

    r = {}
    sums = {}

    sorted_tr = begin
      tr.sort_by { |t| [t.created_at.to_date, t.clinical_user.patient_code, t.visit.visit_date] }
    rescue => e
      tr.sort_by { |t| [t.created_at.to_date, t.clinical_user.patient_code] }
    end

    sorted_tr.each { |ct|
      c_c_c = ct.clinical_user.clinical_center.id
      r[c_c_c].merge!({ct.researcher_id => []}) rescue r[c_c_c] = {ct.researcher_id => []}
      sums[c_c_c].merge!({ct.researcher_id => 0}) rescue sums[c_c_c] = {ct.researcher_id => 0}
    }

    sorted_tr.each do |ct|
      c_c_c = ct.clinical_user.clinical_center.id
      mean, std_dev = ct.visit.get_mean_and_std_devs
      r[c_c_c][ct.researcher_id] << {
        created_at: "#{I18n.l ct.created_at, format: :only_date_dash}",
        patient_code: ct.clinical_user.patient_code,
        visit_date: I18n.l(ct.visit.visit_date, format: :only_date_dash),
        visit_name: ct.visit.name,
        transfer_amount: "#{"%.2f" % ct.amount}",
        mean: mean,
        std_dev: std_dev,
        visit_payment_categorizations: (
          tmp = ""
          ct.visit.visit_payment_categorizations.includes(:visit_payment_category).each {|x|
            tmp << "<b>#{x.try(:visit_payment_category).try(:abbr)}:</b> #{"%.2f" % x.amount}\n"
          }
          tmp[0..-2]
          )
      }
      sums[c_c_c][ct.researcher_id] += ct.amount
    end
    return [r, sums]
  end
end
