class Alior::GenerateFileForTransfers < ApplicationService
  def initialize(transfers:, file_path:, citi_file_id: nil)
    @transfers = transfers
    @file_path = file_path
    @citi_file_id = citi_file_id
  end

  def call
    AliorFile.transaction do
      alior_file = AliorFile::Outgoing.create!(citi_file_id: @citi_file_id, currency: @transfers.last&.final_currency)
      alior_file.attach_transfers_file(transfers: @transfers)
      ClinicalTransfer.where(id: @transfers).update_all(alior_file_id: alior_file.id)
      ClinicalTransfer.where(id: @transfers).each do |ct|
        AliorTransferRelation.create!(clinical_transfer: ct, alior_file: alior_file)
      end
    end
  end
end
