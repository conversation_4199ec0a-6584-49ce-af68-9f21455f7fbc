class ClinicalUsers::SendLmpTransfersToCurrencyAccount < ApplicationService
  def initialize(clinical_user:, currency_account:)
    @clinical_user = clinical_user
    @currency_account = currency_account
  end

  def call
    ActiveRecord::Base.transaction do
      transfers.each do |t|
        t.update!(
          account_number: @currency_account.account_number,
          state: ClinicalTransfer::PROCESSING,
          transfered_to: 'currency_account',
          currency_account: @currency_account
        )
        @currency_account.balance += t.amount
      end
      @currency_account.save!
    end
  end

  private

  def transfers
    @transfers ||= @clinical_user.clinical_transfers.internal_transfers.not_done
  end
end