class Fees::ChargePremiumProjects < ApplicationService
  def initialize(projects: Project.with_premium_plans, date: 1.month.ago.end_of_month)
    @projects = projects
    @date = date
  end

  def call
    puts "Charging premium projects, date: #{ @date }"
    @projects.each do |p|
      next if p.closed

      puts "Handling project #{ p.id }"

      begin
        if !project_already_charged_for_current_month?(project: p)
          Fees::ChargeForPremiumProject.call(project: p, date: @date)
          puts "Success!"
        else
          puts "Project already charged this month, skipping."
        end
      rescue => e
        puts "FAILED! #{ e.to_s }"
        ExceptionNotifier.notify_exception(e, data: { message: "Blad przy pobieraniu oplaty za projekt premium." })
      end
    end
  end

  private

  def project_already_charged_for_current_month?(project:)
    project.fees.for_premium_projects.where(created_at: @date.to_time.utc.all_month).exists?
  end
end
