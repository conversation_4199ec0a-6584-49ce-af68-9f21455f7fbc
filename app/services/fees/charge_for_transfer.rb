class Fees::ChargeForTransfer < ApplicationService
  def initialize(clinical_transfer:)
    @clinical_transfer = clinical_transfer
  end

  def call
    return if @clinical_transfer.is_refund || @clinical_transfer.child_transfers.exists?(is_refund: true)

    Fee.create!(
      project_id: @clinical_transfer.project_id,
      state: Fee::STATES['Charged'],
      amount: amount,
      type_name: Fee::TYPE_NAMES['Clinical Transfer'],
      clinical_transfer_id: @clinical_transfer.id
    )
  end

  private

  def amount
    return @clinical_transfer.project.fee_post_transfer if @clinical_transfer.by_post?

    tier = Country.tier(@clinical_transfer.country_name)
    raise "Country #{ @clinical_transfer.country_name } has no tier!" unless tier
    @clinical_transfer.send "tier_#{ tier }_transfer_fee"
  end
end