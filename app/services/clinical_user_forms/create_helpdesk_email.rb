class ClinicalUserForms::C<PERSON>HelpdeskEmail < ApplicationService
  delegate_missing_to :@clinical_user_form

  def initialize(clinical_user_form:)
    @clinical_user_form = clinical_user_form
  end

  def call
    HelpdeskEmail.transaction do
      create_helpdesk_email
      create_attachment
      send_sms
      send_email
    end
  end

  private

  def create_helpdesk_email
    @helpdesk_email = HelpdeskEmail.create!(
      clinical_user_form: @clinical_user_form,
      researcher: researcher,
      project: project,
      contract_research_organization: contract_research_organization,
      subject: subject,
      body: body,
      from: researcher.email,
      to: '<EMAIL>',
      message_date: Time.current,
      skip_autorespond: true,
      skip_fix_if_forward: true,
      request_type: 'Form'
    )
  end

  def create_attachment
    return unless @clinical_user_form.file.present?

    @helpdesk_email
      .helpdesk_email_attachments
      .create!(
        file: @clinical_user_form.file.blob
      )
  end

  def send_sms
    researcher.send_sms("PAYCLINICAL.COM: Thank you for uploading the personal data form for subject #{patient_code} in #{clinical_protocol_code} clinical study.")
  end

  def send_email
    HelpdeskMailer.with(clinical_user_form: @clinical_user_form).user_form_uploaded.deliver_now
  end

  def subject
    I18n.t('helpdesk_email.from_clinical_user_form.subject', patient_code: patient_code, protocol: clinical_protocol_code)
  end

  def body
    result = I18n.t('helpdesk_email.from_clinical_user_form.body', patient_code: patient_code, protocol: clinical_protocol_code, site: clinical_center_code, researcher: researcher.full_name)

    if researcher.phone_number.present?
      result << I18n.t('helpdesk_email.from_clinical_user_form.body_phone', phone: researcher.phone_number)
    end

    result
  end
end