class ActionItems::Escalations::First < ApplicationService
  delegate_missing_to :@action_item

  def initialize(action_item:)
    @action_item = action_item
  end

  def call
    result = if owner == 'Investigator'
      ActionItems::Targets::Cras::GetTo.call(action_item: @action_item)
    elsif owner == 'CRA'
      ActionItems::Targets::Managers::GetTo.call(action_item: @action_item)
    elsif owner == 'Manager'
      cro.super_managers.first
    elsif owner == 'Operator'
      Researcher.operators
    end

    Array(result).first
  end
end