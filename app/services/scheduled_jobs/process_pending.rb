class ScheduledJobs::ProcessPending < ApplicationService
  def initialize(pending_jobs: ScheduledJob.pending.unstarted)
    @pending_jobs = pending_jobs
  end

  def call
    @pending_jobs.each do |job|
      process_job(job)
    end
  end

  private

  def process_job(job)
    puts "Starting job #{ job.id } with command #{ job.command }"

    if job.reload.started_at.present?
      puts 'Job already started, skipping.'
      return
    end

    job.update_column :started_at, Time.now
    # rubocop:disable Security/Eval
    eval(job.command)
    # rubocop:enable Security/Eval
    ScheduledJob.where(id: job.id).update_all(
      finished_at: Time.now,
      state: 'success'
    )
  rescue => e
    puts 'FAILED'
    puts e.message
    ScheduledJob.where(id: job.id).update_all(
      state: 'failed',
      finished_at: Time.now,
      error: e.message.to_s,
      backtrace: e.backtrace.to_s
    )
    ExceptionNotifier.notify_exception(e, data: { message: 'Processing background job failed.' })
  end
end