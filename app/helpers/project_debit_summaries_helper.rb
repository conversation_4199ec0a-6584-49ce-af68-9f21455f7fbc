module ProjectDebitSummariesHelper
  def pds_pdf_download_link(pds:)
    pdf_available = pds.file_path.present?
    path = pdf_available ? v2_sponsor_project_project_debit_summary_pdfs_path(pds.project, pds) : 'javascript:;'
    klass = pdf_available ? '' : 'disabled'
    link_to 'Download', path, class: "dropdown-item #{ klass }", target: '_blank'
  end

  def pd_summary_state_color(pd_summary:)
    if pd_summary.credit_unpaid?
      '#00b9f5'
    elsif pd_summary.sent_at && pd_summary.overdue?
      "#e02361"
    elsif pd_summary.sent_at && !pd_summary.overdue?
      "#d46610"
    elsif pd_summary.paid?
      "#03b516"
    else
      'black'
    end
  end

  def pds_state_text(pds:)
    pds.credit_unpaid? ? 'Credited' : pds.state_human_name
  end

  def pd_summary_status_alert_class(pd_summary)
    if pd_summary.sent_at
      'red_color'
    end
  end
end
