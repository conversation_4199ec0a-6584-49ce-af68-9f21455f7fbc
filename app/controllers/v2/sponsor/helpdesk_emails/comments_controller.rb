class V2::Sponsor::HelpdeskEmails::CommentsController < ApplicationController
  before_action :authenticate_researcher!
  before_action :set_email

  def new
    @comment = Comment.new
  end

  def create
    @comment = @email.comments.new(comment_params)
    @comment.added_by = current_researcher

    if @comment.save
      redirect_to v2_sponsor_helpdesk_emails_path, notice: 'Comment has been added.'
    else
      render :new
    end
  end

  def edit
    @comment = Comment.find(params[:id])
    authorize @comment
  end

  def update
    @comment = Comment.find(params[:id])
    authorize @comment, :edit?

    if @comment.update(comment_params)
      redirect_to v2_sponsor_helpdesk_email_path(@email), notice: 'Comment was successfully updated.'
    else
      render :edit
    end
  end

  def destroy
    @comment = Comment.find(params[:id])
    authorize @comment, :edit?

    @comment.destroy!
    redirect_to v2_sponsor_helpdesk_email_path(@email), notice: 'Comment was deleted.'
  end

  private

  def set_email
    @email = HelpdeskEmail.find(params[:helpdesk_email_id])
  end

  def comment_params
    params[:comment].permit(:body)
  end
end