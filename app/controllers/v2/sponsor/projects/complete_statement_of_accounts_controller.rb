class V2::Sponsor::Projects::CompleteStatementOfAccountsController < ApplicationController
  before_action :authenticate_researcher!
  before_action :set_project
  layout 'statement_of_account'

  def new
    authorize @project, :show?
  end

  def create
    authorize @project, :show?
    doc_type = params[:doc_type]
    if doc_type == 'xlsx'
      gen_xlsx
    else
      gen_pdf
    end
  end

  private

  def gen_pdf
    pdf = ProjectTransactionHistoryPdf.new(project_id: @project.id, start_date: @project.created_at.to_date,
      end_date: Date.today)
    send_data pdf.render,
    :filename => "complete_statement_of_account.pdf",
    :type => 'application/pdf', :disposition => 'inline'
  end

  def gen_xlsx
    @start_date = @project.created_at.to_date
    @end_date = Date.today
    @transactions = ProjectTransfers.call(project_id: @project.id, date_range: @start_date..@end_date)
    render xlsx: 'create', filename: "Account Statement #{ @project.clinical_protocol_code } #{ @project.created_at.strftime('%d-%m-%Y') } - #{ Date.today.strftime('%d-%m-%Y') }.xlsx", disposition: 'inline'
  end
end
