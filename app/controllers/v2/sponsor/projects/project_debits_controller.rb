class V2::Sponsor::Projects::ProjectDebitsController < ApplicationController
  before_action :authenticate_researcher!
  before_action :set_project

  def index
    authorize! :read, :project_debits
    allowed_scopes = ['waiting']
    scope = params[:scope]
    if scope and allowed_scopes.include? scope
      @project_debits = @project.project_debits.send(scope).page(params[:page])
    else
      @project_debits = @project.project_debits.page(params[:page])
    end
    @current_role = current_researcher.role_in_project(@project).try(:project_role)
  end
end
