class V2::Sponsor::PayclinicalEmployees::ProjectCodesController < V2::Sponsor::PayclinicalEmployees::BaseController
  before_action :set_project

  def edit; end

  def update
    if @project.update(project_params)
      redirect_to v2_sponsor_project_path(@project), notice: 'Project code has been updated.'
    else
      render :edit
    end
  end

  private

  def set_project
    @project = current_researcher.projects.find(params[:id])
  end

  def project_params
    params[:project].permit(:project_code)
  end
end
