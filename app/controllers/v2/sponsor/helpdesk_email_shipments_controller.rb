class V2::Sponsor::HelpdeskEmailShipmentsController < ApplicationController
  before_action :authenticate_researcher!
  before_action :find_email_and_authorize

  def new
    set_current_researcher_as_operator

    @object = HelpdeskEmailShipmentForm.new(form_params)
    @object.project_id ||= @email.project_id
    @object.helpdesk_email_id = @email.id
    @object.set_sender_email
    @object.set_delivered_on

    set_data_for_new_object
    respond_to :html, :js
  end

  def create
    @object = HelpdeskEmailShipmentForm.new(form_params)
    @object.helpdesk_email_id = @email.id
    @object.acting_user = current_researcher

    set_data_for_new_object

    if @object.save
      flash[:notice] = HelpdeskEmails::GetAfterShipmentMessage.call(helpdesk_email: @email)
      redirect_to v2_sponsor_helpdesk_emails_path
    else
      flash[:notice] = @object.errors.full_messages.to_sentence
      render :new
    end
  end

  def find_email_and_authorize
    @email = HelpdeskEmail.find(params[:helpdesk_email_id])
    authorize @email, :create_shipment?
  end

  def set_current_researcher_as_operator
    @email.update(operator_id: current_researcher.id)
  end

  def set_data_for_new_object
    @projects = @email.contract_research_organization ? @email.contract_research_organization.projects.order('clinical_protocol_code asc') : current_researcher.projects.order('clinical_protocol_code asc')
    @clinical_centers = current_researcher.clinical_centers.where(project_id: @object.project_id).order('clinical_centers.clinical_center_code asc') if @object.project_id
  end

  private

  def form_params
    params[:helpdesk_email_shipment_form]&.permit(
      :project_id,
      :clinical_center_id,
      :delivered_on,
      :sender_email,
      :subject_numbers,
      :patient_codes_for_receipts
    )
  end
end
