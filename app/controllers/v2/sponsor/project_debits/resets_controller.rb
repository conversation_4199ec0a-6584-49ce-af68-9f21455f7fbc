

class V2::Sponsor::ProjectDebits::ResetsController < ApplicationController
  before_action :authenticate_researcher!

  def create
    project_debit = ProjectDebit.find params[:project_debit_id]
    authorize project_debit, :reset?
    form = ProjectDebitResetForm.new(project_debit: project_debit, researcher: current_researcher)
    if form.save
      redirect_back_with_default notice: 'All payments have been cancelled.'
    else
      redirect_back_with_default alert: form.errors.full_messages.to_sentence
    end
  end
end
