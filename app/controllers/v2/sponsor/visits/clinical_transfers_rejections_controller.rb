class V2::Sponsor::Visits::ClinicalTransfersRejectionsController < ApplicationController
  before_action :authenticate_researcher!
  before_action :load_resources

  def new
    authorize @clinical_transfer, :reject?
    unless @clinical_transfer
      redirect_to v2_sponsor_clinical_user_visits_path(@clinical_user), alert: flash_msg_for(action_result_type: 'not_found') and return
    end
  end

  def create
    authorize @clinical_transfer, :reject?
    @comment = params[:visit][:comment]

    if ActionView::Base.full_sanitizer.sanitize(@visit.comment).blank? and ActionView::Base.full_sanitizer.sanitize(@comment).blank?
      @visit.errors.add(:comment, flash_msg_for(action_result_type: 'provide_reason'))
      render :new and return
    end

    reject_transfer
    redirect_to v2_sponsor_clinical_user_visits_path(@visit.clinical_user), notice: flash_msg_for
  rescue => e
    ExceptionNotifier.notify_exception(e, data: { message: "Nieudana proba odrzucenia przelewu za wizyte #{@visit.id} przez bad<PERSON><PERSON> #{ current_researcher.id}" })
    render :new, alert: flash_msg_for(action_result_type: 'fail')
  end

  private

  def reject_transfer
    ClinicalTransfer.transaction do
      @clinical_transfer.reject(rejecting_researcher: current_researcher, reject_reason: @comment)
      @visit.update!(comment: @comment)
    end
  end

  def load_resources
    @visit = Visit.find(params[:visit_id])
    @clinical_transfer = @visit.clinical_transfers.waiting.last
    @clinical_user = @visit.clinical_user
    @project = @clinical_transfer.project #potrzebne do autoryzacji
  end
end
