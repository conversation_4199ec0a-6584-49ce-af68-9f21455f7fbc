

class V2::Sponsor::ClinicalTransfers::DestinationsController < ApplicationController
  before_action :authenticate_researcher!

  helper_method :otp_required?

  def edit
    set_data_and_authorize
  end

  def update
    set_data_and_authorize

    if destination_updated?
      redirect_to v2_sponsor_clinical_transfers_path, notice: 'Payment has been updated.'
    else
      flash.now[:alert] = @errors.to_sentence
      render :edit
    end
  end

  private

  def otp_required?
    false
  end

  def set_data_and_authorize
    @clinical_transfer = ClinicalTransfer.find params[:clinical_transfer_id]
    authorize @clinical_transfer, :change_destination?

    @clinical_user = @clinical_transfer.clinical_user
    @visit = @clinical_transfer.visit

    unless @visit.destination_can_be_changed?
      redirect_back_with_default(alert: 'The transfer destination cannot be changed.') && return
    end

    @view = NewVisitPaymentView.new(researcher: current_researcher, visit: @visit, clinical_user: @clinical_user, disable_priority: true)
  end

  def destination_updated?
    @errors = []
    Visit.transaction do
      @clinical_transfer.transfered_to = params[:transfer_to]
      unless @clinical_transfer.save
        @errors << @clinical_transfer.errors.full_messages.to_sentence
        raise 'error'
      end

      @visit.transfer_to = params[:transfer_to]
      unless @visit.save
        @errors << @visit.errors.full_messages.to_sentence
        raise 'error'
      end
    end
    true
  rescue StandardError => e
    false
  end
end
