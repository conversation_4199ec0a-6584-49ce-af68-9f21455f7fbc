

class V2::Sponsor::UserActionRequirementsController < ApplicationController
  before_action :authenticate_researcher!

  def index
    @user_action_requirements = UserActionRequirement.where(clinical_user_id: current_researcher.clinical_users).with_state(:new, :pending).includes(:project, :clinical_center, :clinical_user).order('projects.clinical_protocol_code, clinical_centers.clinical_center_code, clinical_users.patient_code asc, user_action_requirements.created_at desc')
  end

  def set_as_pending
    user_action_requirement = UserActionRequirement.find(params[:user_action_requirement_id])
    authorize user_action_requirement, :set_as_pending?

    if user_action_requirement.update!(state: 'pending', finished_at: nil)
      flash[:notice] = 'Status of this issue has been changed to pending.'
      redirect_back_with_default
    end
  rescue StandardError => e
    ExceptionNotifier.notify_exception(e, data: { message: 'Nieudana zmiany statusu user action requirement' })
    redirect_back_with_default(alert: t('v2.flash.unexpected_error')) && return
  end

  def set_as_resolved
    user_action_requirement = UserActionRequirement.find(params[:user_action_requirement_id])
    authorize user_action_requirement, :set_as_resolved?

    if user_action_requirement.update!(state: 'resolved', finished_at: DateTime.now)
      flash[:notice] = 'Status of this issue has been changed to resolved.'
      redirect_back_with_default
    end
  rescue StandardError => e
    ExceptionNotifier.notify_exception(e, data: { message: 'Nieudana zmiany statusu user action requirement' })
    redirect_back_with_default(alert: t('v2.flash.unexpected_error')) && return
  end
end
