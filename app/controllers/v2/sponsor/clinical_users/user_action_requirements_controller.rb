class V2::Sponsor::ClinicalUsers::UserActionRequirementsController < ApplicationController
  before_action :authenticate_researcher!
  before_action :get_clinical_user_and_action_requirement, only: [:set_as_pending, :set_as_resolved]

  def new
    @clinical_user = ClinicalUser.find(params[:clinical_user_id])
    @user_action_requirement = UserActionRequirement.new
    authorize @clinical_user, :add_action_requirement?
  end

  def create
    @clinical_user = ClinicalUser.find(params[:clinical_user_id])
    authorize @clinical_user, :add_action_requirement?
    @user_action_requirement = UserActionRequirement.new(params[:user_action_requirement])
    @user_action_requirement.researcher_id = current_researcher.id
    @user_action_requirement.clinical_user_id = @clinical_user.id

    if @user_action_requirement.save
      redirect_to [:v2, :sponsor, @clinical_user, :visits], notice: 'Action has been added.'
    else
      render :new
    end
  end

  private

  def get_clinical_user_and_action_requirement
    @clinical_user = ClinicalUser.find(params[:clinical_user_id])
    @user_action_requirement = @clinical_user.user_action_requirements.find(params[:user_action_requirement_id])
  end
end
