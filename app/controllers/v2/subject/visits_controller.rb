

class V2::Subject::VisitsController < V2::SubjectController
  include V2::SharedVisitController
  before_action :authenticate_clinical_user!

  def index
    @visits = if params[:only_waiting] == 'true'
                current_clinical_user.sorted_visits.select { |v| v.state == 'w' }
              else
                current_clinical_user.sorted_visits
              end
    @all_visits = current_clinical_user.visits
    @visits = Kaminari.paginate_array(@visits).page(params[:page])
    @visits_amount = VisitPaymentCategorization.where(visit_id: @visits.map(&:id)).sum(:amount)
    @activities_past_year = current_clinical_user.activities_past_year_by_month
    @visits_paid_past_year = current_clinical_user.visits_paid_past_year_by_month
  end

  def show
    @visit = current_clinical_user.visits.find(params[:id])
    @visit.set_probable_costs(researcher: current_researcher)
  end

  def update
    @visit = current_clinical_user.visits.find(params[:id])

    if visit_payment_categorizations_empty_but_other_info_present?
      @visit.errors.add(:visit_payment_categorizations, '<PERSON><PERSON><PERSON> wy<PERSON> kategorię kosztu..')
      render(:show) && return
    end

    unless @visit.not_happened?
      redirect_back_with_default(alert: 'Nie mo<PERSON>na modyfikować tej wizyty.') && return
    end

    params[:visit].except!(:visit_happened, :action_type)

    if @visit.  update_attributes_with_windows(params[:visit], nil, params[:visit][:visit_payment_categorizations_attributes].try(:values), changed_by: current_clinical_user)
      redirect_to v2_subject_visit_path(@visit), notice: flash_msg_for(message_vars: { visit_name: @visit.name })
    else
      render :show
    end
  end
end
