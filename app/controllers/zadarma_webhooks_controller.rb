class ZadarmaWebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token

  def create
    return render plain: params[:zd_echo] if params[:zd_echo].present?

    if params[:event].blank?
      Rails.logger.info "Zadarma webhook received without event: #{params.to_s}"
      return head :ok
    end

    ZadarmaEvent.create!(
      event: params[:event],
      call_start: params[:call_start],
      pbx_call_number: params[:pbx_call_id],
      caller_number: params[:caller_id],
      called_number: params[:called_did],
    )

    head :ok
  end
end
