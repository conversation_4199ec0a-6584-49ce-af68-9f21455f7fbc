module ClinicalTransfers::Acceptance::AddFundsRedirects
  extend ActiveSupport::Concern

  def redirect_to_add_funds_if_not_enough_funds_for_transfer_acceptance
    project = @clinical_transfer.project

    return unless policy(project).add_funds?
    return if project.debit_allowed

    currency_account = project.currency_account(project.currency)

    if currency_account.balance < @clinical_transfer.amount
      amount_for_all_unaccepted_transfers = ClinicalTransfer
        .where(project: current_researcher.projects)
        .waiting
        .sum(:amount) -
        currency_account.balance

      redirect_to new_v2_sponsor_project_add_funds_path(project, internal_transfer_to_project: { amount: amount_for_all_unaccepted_transfers }), notice: 'You do not have sufficient funds to approve the payment. Please add funds from another account.' and return
    end
  end
end