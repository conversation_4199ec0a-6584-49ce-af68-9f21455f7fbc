table.table
  thead
    tr
      th = resource_code
      th Pending
      th Level 1 Escalation
      th Level 2 Escalation
      th Completed
      th.fit_to_content
  tbody
    - @action_items.group_by { |ai| ai.send(group_by_resource) }.each do |group_resource, items|
      tr
        td = group_resource_link(group_resource)
        td
          = item_count_row(items: items, scope: :pending?, group_resource: group_resource)
          span.row_second_line = item_count_second_row(items: items, scope: :finishing_pending?, group_resource: group_resource, text: 'due soon')
        td
          = item_count_row(items: items, scope: :lvl_1_escalation?, group_resource: group_resource)
          span.row_second_line = item_count_second_row(items: items, scope: :finishing_lvl_1_escalation?, group_resource: group_resource, text: 'due soon')
        td
          = item_count_row(items: items, scope: :lvl_2_escalation?, group_resource: group_resource)
          span.row_second_line = item_count_second_row(items: items, scope: :finishing_lvl_2_escalation?, group_resource: group_resource, text: 'overdue')
        td
          = item_count_row(items: items, scope: :completed?, group_resource: group_resource)
        td = render DropdownBtnComponent.new do |component|
          - component.with_links do
            = link_to 'All unresolved', dropdown_link_path(group_resource: group_resource, scope: 'not_resolved'), class: 'dropdown-item'
            .dropdown-divider
            = link_to 'Pending', dropdown_link_path(group_resource: group_resource, scope: 'pending'), class: 'dropdown-item'
            = link_to 'Level 1 Escalation', dropdown_link_path(group_resource: group_resource, scope: 'lvl_1_escalation'), class: 'dropdown-item'
            = link_to 'Level 2 Escalation', dropdown_link_path(group_resource: group_resource, scope: 'lvl_2_escalation'), class: 'dropdown-item'
            .dropdown-divider
            = link_to 'Resolved', dropdown_link_path(group_resource: group_resource, scope: 'completed'), class: 'dropdown-item'

