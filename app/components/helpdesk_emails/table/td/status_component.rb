class HelpdeskEmails::Table::Td::StatusComponent < ViewComponent::Base
  delegate_missing_to :@helpdesk_email

  def initialize(helpdesk_email:)
    @helpdesk_email = helpdesk_email
  end

  def status_span_class
    if status == 'new'
      'red'
    end
  end

  def request_type_or_cro_operators
    if status == 'new'
      cro_operators_initials
    else
      [request_type, task_category&.name].compact.join('/')
    end
  end

  def cro_operators_initials
    if cro_has_operators?
      cro_operators_initials_with_tooltip
    else
      'None/None'
    end
  end

  private

  def operator_initials(operator = nil)
    return 'None' unless operator

    result = ''
    result << operator.first_name&.first
    result << operator.last_name&.first
    result
  end

  def cro_has_operators?
    contract_research_organization&.primary_operator || contract_research_organization&.secondary_operator
  end

  def cro_operators_initials_with_tooltip
    initials = []
    names = []

    initials << operator_initials(contract_research_organization&.primary_operator)
    initials << operator_initials(contract_research_organization&.secondary_operator)

    names << contract_research_organization&.primary_operator&.full_name
    names << contract_research_organization&.secondary_operator&.full_name
    tooltip_title = names.compact.join('/')

    tag.span data: { toggle: 'tooltip', title: tooltip_title } do
      initials.compact.join('/')
    end
  end
end
