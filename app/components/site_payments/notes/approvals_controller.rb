class SitePayments::Notes::ApprovalsController < SitePayments::BaseController
  before_action :set_note

  def new
    authorize @note, :approve?, policy_class: SitePayments::NotePolicy
  end

  def create
    authorize @note, :approve?, policy_class: SitePayments::NotePolicy

    @note.approved_at = Time.current
    @note.approved_by_id = current_researcher.id
    @note.assign_attributes(params[:site_payments_note_for_manager_approval])

    if @note.save
      redirect_to site_payments_notes_path(clinical_center_id: @note.clinical_center_id), notice: 'Note was approved successfully.'
    else
      render :new
    end
  end

  private

  def set_note
    @note = SitePayments::Note
      .where(project: current_researcher.projects)
      .find(params[:note_id])
      .becomes(SitePayments::Note::ForManagerApproval)
  end
end